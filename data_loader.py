import pandas as pd
import backtrader as bt
from datetime import datetime
import os


class ETFDataLoader:
    """ETF数据加载器，用于读取和处理本地CSV数据"""
    
    def __init__(self, data_dir="."):
        """
        初始化数据加载器
        Args:
            data_dir: 数据文件所在目录
        """
        self.data_dir = data_dir
        
    def load_etf_data(self, file_path):
        """
        加载单个ETF的CSV数据
        Args:
            file_path: CSV文件路径
        Returns:
            处理后的DataFrame
        """
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 解析时间字段
        df['datetime'] = pd.to_datetime(
            df['date'].astype(str) + ' ' + 
            df['time'].astype(str).str.zfill(9).str[:-2].str[:2] + ':' +
            df['time'].astype(str).str.zfill(9).str[:-2].str[2:4] + ':' +
            df['time'].astype(str).str.zfill(9).str[:-2].str[4:6]
        )
        
        # 设置datetime为索引
        df.set_index('datetime', inplace=True)
        
        # 重命名列以符合backtrader要求
        df.rename(columns={
            'open': 'open',
            'high': 'high', 
            'low': 'low',
            'last': 'close',  # 使用last作为收盘价
            'total_volume_trade': 'volume'
        }, inplace=True)
        
        # 选择需要的列
        df = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # 确保数据类型正确
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')/10000
        
        # 删除包含NaN的行
        df.dropna(inplace=True)
        
        # 按时间排序
        df.sort_index(inplace=True)
        
        return df
    
    def create_backtrader_feed(self, file_path, name=None):
        """
        创建Backtrader数据源
        Args:
            file_path: CSV文件路径
            name: 数据源名称
        Returns:
            Backtrader数据源对象
        """
        df = self.load_etf_data(file_path)
        
        # 创建Backtrader数据源
        data_feed = bt.feeds.PandasData(
            dataname=df,
            name=name or os.path.basename(file_path).split('.')[0],
            timeframe=bt.TimeFrame.Minutes,
            compression=1,
            fromdate=df.index[0],
            todate=df.index[-1]
        )
        
        return data_feed
    
    def get_combined_data_for_analysis(self, file_paths, etf_names=None):
        """
        获取合并的数据用于策略分析（非Backtrader使用）
        Args:
            file_paths: CSV文件路径列表
            etf_names: ETF名称列表
        Returns:
            合并后的DataFrame，包含两个ETF的开盘价
        """
        if etf_names is None:
            etf_names = [f"ETF_{i+1}" for i in range(len(file_paths))]
        
        combined_data = {}
        
        for i, file_path in enumerate(file_paths):
            df = self.load_etf_data(file_path)
            # 只使用开盘价进行配对交易分析
            combined_data[etf_names[i]] = df['open']
        
        # 合并数据
        result_df = pd.DataFrame(combined_data)
        
        # 删除任何包含NaN的行（确保两个ETF在同一时间都有数据）
        result_df.dropna(inplace=True)
        
        return result_df
    
    def print_data_info(self, file_path):
        """
        打印数据文件的基本信息
        Args:
            file_path: CSV文件路径
        """
        df = self.load_etf_data(file_path)
        
        print(f"\n=== {file_path} 数据信息 ===")
        print(f"数据形状: {df.shape}")
        print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
        print(f"数据列: {list(df.columns)}")
        print(f"前5行数据:")
        print(df.head())
        print(f"数据统计:")
        print(df.describe())


if __name__ == "__main__":
    # 测试数据加载器
    loader = ETFDataLoader()
    
    # 测试文件路径
    file1 = "hq-shkl-518880-6-20250227170356623.csv"
    file2 = "hq-szkl-160719-6-20250716151335503.csv"
    
    if os.path.exists(file1) and os.path.exists(file2):
        # 打印数据信息
        loader.print_data_info(file1)
        loader.print_data_info(file2)
        
        # 测试合并数据
        combined_data = loader.get_combined_data_for_analysis(
            [file1, file2], 
            ['ETF_518880', 'ETF_160719']
        )
        
        print(f"\n=== 合并数据信息 ===")
        print(f"合并数据形状: {combined_data.shape}")
        print(f"时间范围: {combined_data.index[0]} 到 {combined_data.index[-1]}")
        print(f"前5行合并数据:")
        print(combined_data.head())
        
    else:
        print("数据文件不存在，请检查文件路径")
