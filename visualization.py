#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配对交易策略结果可视化模块
生成策略收益曲线、价差走势、信号点位等图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from datetime import datetime
import matplotlib.dates as mdates
from data_loader import ETFDataLoader

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class PairsVisualization:
    """配对交易策略可视化类"""
    
    def __init__(self, output_dir="results"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def load_backtest_results(self, signals_file, trades_file):
        """
        加载回测结果数据
        Args:
            signals_file: 信号记录文件路径
            trades_file: 交易记录文件路径
        """
        self.signals_df = pd.read_csv(signals_file)
        self.signals_df['datetime'] = pd.to_datetime(self.signals_df['datetime'])
        self.signals_df.set_index('datetime', inplace=True)
        
        self.trades_df = pd.read_csv(trades_file)
        self.trades_df['datetime'] = pd.to_datetime(self.trades_df['datetime'])
        
        print(f"加载信号记录: {len(self.signals_df)} 条")
        print(f"加载交易记录: {len(self.trades_df)} 条")
    
    def plot_price_ratio_and_signals(self, save_path=None):
        """
        绘制价格比率和交易信号图
        Args:
            save_path: 保存路径，如果为None则显示图表
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 上图：价格比率和交易边界
        ax1.plot(self.signals_df.index, self.signals_df['price_ratio'], 
                label='价格比率 (ETF1/ETF2)', color='blue', linewidth=1)
        ax1.plot(self.signals_df.index, self.signals_df['upper_bound'], 
                label='上边界', color='red', linestyle='--', alpha=0.7)
        ax1.plot(self.signals_df.index, self.signals_df['lower_bound'], 
                label='下边界', color='green', linestyle='--', alpha=0.7)
        
        # 填充交易区间
        ax1.fill_between(self.signals_df.index, 
                        self.signals_df['upper_bound'], 
                        self.signals_df['lower_bound'], 
                        alpha=0.1, color='gray', label='交易区间')
        
        # 标记交易信号点
        buy_signals = self.signals_df[self.signals_df['trade_signal'] == 1]
        sell_signals = self.signals_df[self.signals_df['trade_signal'] == -1]
        
        if not buy_signals.empty:
            ax1.scatter(buy_signals.index, buy_signals['price_ratio'], 
                       color='green', marker='^', s=30, alpha=0.7, label='买入信号')
        
        if not sell_signals.empty:
            ax1.scatter(sell_signals.index, sell_signals['price_ratio'], 
                       color='red', marker='v', s=30, alpha=0.7, label='卖出信号')
        
        ax1.set_title('配对交易策略 - 价格比率与交易信号', fontsize=14, fontweight='bold')
        ax1.set_ylabel('价格比率', fontsize=12)
        ax1.legend(loc='upper right')
        ax1.grid(True, alpha=0.3)
        
        # 下图：ETF价格走势
        ax2.plot(self.signals_df.index, self.signals_df['etf1_price'], 
                label='ETF1 价格', color='blue', linewidth=1)
        ax2_twin = ax2.twinx()
        ax2_twin.plot(self.signals_df.index, self.signals_df['etf2_price'], 
                     label='ETF2 价格', color='orange', linewidth=1)
        
        ax2.set_title('ETF价格走势', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间', fontsize=12)
        ax2.set_ylabel('ETF1 价格', fontsize=12, color='blue')
        ax2_twin.set_ylabel('ETF2 价格', fontsize=12, color='orange')
        
        # 设置时间轴格式
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='upper left')
        ax2_twin.legend(loc='upper right')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"价格比率图已保存: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def plot_portfolio_performance(self, initial_cash=1000000, save_path=None):
        """
        绘制组合收益曲线
        Args:
            initial_cash: 初始资金
            save_path: 保存路径
        """
        # 计算累计收益
        self.trades_df['cumulative_pnl'] = self.trades_df['pnlcomm'].cumsum()
        self.trades_df['portfolio_value'] = initial_cash + self.trades_df['cumulative_pnl']
        self.trades_df['returns'] = (self.trades_df['portfolio_value'] / initial_cash - 1) * 100
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 上图：组合价值走势
        ax1.plot(self.trades_df['datetime'], self.trades_df['portfolio_value'], 
                color='blue', linewidth=2, label='组合价值')
        ax1.axhline(y=initial_cash, color='red', linestyle='--', alpha=0.7, label='初始资金')
        
        ax1.set_title('配对交易策略 - 组合价值走势', fontsize=14, fontweight='bold')
        ax1.set_ylabel('组合价值 (元)', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        
        # 下图：累计收益率
        ax2.plot(self.trades_df['datetime'], self.trades_df['returns'], 
                color='green', linewidth=2, label='累计收益率')
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        ax2.set_title('累计收益率走势', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间', fontsize=12)
        ax2.set_ylabel('收益率 (%)', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 设置时间轴格式
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"收益曲线图已保存: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def plot_trade_analysis(self, save_path=None):
        """
        绘制交易分析图
        Args:
            save_path: 保存路径
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 左上：每笔交易盈亏分布
        pnl_data = self.trades_df['pnlcomm']
        ax1.hist(pnl_data, bins=50, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title('每笔交易盈亏分布', fontsize=12, fontweight='bold')
        ax1.set_xlabel('盈亏 (元)')
        ax1.set_ylabel('频次')
        ax1.grid(True, alpha=0.3)
        
        # 右上：盈利vs亏损交易统计
        profit_trades = len(pnl_data[pnl_data > 0])
        loss_trades = len(pnl_data[pnl_data < 0])
        
        ax2.pie([profit_trades, loss_trades], 
               labels=[f'盈利交易\n{profit_trades}笔', f'亏损交易\n{loss_trades}笔'],
               colors=['green', 'red'], autopct='%1.1f%%', startangle=90)
        ax2.set_title('盈亏交易比例', fontsize=12, fontweight='bold')
        
        # 左下：月度收益分布
        self.trades_df['month'] = self.trades_df['datetime'].dt.to_period('M')
        monthly_pnl = self.trades_df.groupby('month')['pnlcomm'].sum()
        
        colors = ['green' if x > 0 else 'red' for x in monthly_pnl.values]
        ax3.bar(range(len(monthly_pnl)), monthly_pnl.values, color=colors, alpha=0.7)
        ax3.set_title('月度收益分布', fontsize=12, fontweight='bold')
        ax3.set_xlabel('月份')
        ax3.set_ylabel('月度收益 (元)')
        ax3.grid(True, alpha=0.3)
        
        # 设置x轴标签
        ax3.set_xticks(range(0, len(monthly_pnl), max(1, len(monthly_pnl)//10)))
        ax3.set_xticklabels([str(monthly_pnl.index[i]) for i in range(0, len(monthly_pnl), max(1, len(monthly_pnl)//10))], 
                           rotation=45)
        
        # 右下：交易频率分析
        self.trades_df['date'] = self.trades_df['datetime'].dt.date
        daily_trades = self.trades_df.groupby('date').size()
        
        ax4.hist(daily_trades.values, bins=20, alpha=0.7, color='purple', edgecolor='black')
        ax4.set_title('日交易频率分布', fontsize=12, fontweight='bold')
        ax4.set_xlabel('每日交易次数')
        ax4.set_ylabel('天数')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"交易分析图已保存: {save_path}")
        else:
            plt.show()
        
        plt.close()
    
    def generate_performance_report(self, initial_cash=1000000):
        """
        生成绩效报告
        Args:
            initial_cash: 初始资金
        """
        print("\n" + "="*60)
        print("配对交易策略绩效报告")
        print("="*60)
        
        # 基本统计
        total_trades = len(self.trades_df)
        profit_trades = len(self.trades_df[self.trades_df['pnlcomm'] > 0])
        loss_trades = len(self.trades_df[self.trades_df['pnlcomm'] < 0])
        win_rate = profit_trades / total_trades * 100 if total_trades > 0 else 0
        
        total_pnl = self.trades_df['pnlcomm'].sum()
        final_value = initial_cash + total_pnl
        total_return = (final_value / initial_cash - 1) * 100
        
        avg_profit = self.trades_df[self.trades_df['pnlcomm'] > 0]['pnlcomm'].mean() if profit_trades > 0 else 0
        avg_loss = self.trades_df[self.trades_df['pnlcomm'] < 0]['pnlcomm'].mean() if loss_trades > 0 else 0
        
        print(f"回测期间: {self.signals_df.index[0].strftime('%Y-%m-%d')} 至 {self.signals_df.index[-1].strftime('%Y-%m-%d')}")
        print(f"初始资金: {initial_cash:,.2f} 元")
        print(f"最终资金: {final_value:,.2f} 元")
        print(f"总收益: {total_pnl:,.2f} 元")
        print(f"总收益率: {total_return:.2f}%")
        print(f"总交易次数: {total_trades}")
        print(f"盈利交易: {profit_trades} 笔")
        print(f"亏损交易: {loss_trades} 笔")
        print(f"胜率: {win_rate:.2f}%")
        print(f"平均盈利: {avg_profit:.2f} 元")
        print(f"平均亏损: {avg_loss:.2f} 元")
        
        if avg_loss != 0:
            profit_loss_ratio = abs(avg_profit / avg_loss)
            print(f"盈亏比: {profit_loss_ratio:.2f}")
        
        print("="*60)
    
    def create_all_visualizations(self, signals_file, trades_file, initial_cash=1000000):
        """
        创建所有可视化图表
        Args:
            signals_file: 信号文件路径
            trades_file: 交易文件路径
            initial_cash: 初始资金
        """
        # 加载数据
        self.load_backtest_results(signals_file, trades_file)
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 生成各种图表
        self.plot_price_ratio_and_signals(
            os.path.join(self.output_dir, f"price_ratio_signals_{timestamp}.png")
        )
        
        self.plot_portfolio_performance(
            initial_cash, 
            os.path.join(self.output_dir, f"portfolio_performance_{timestamp}.png")
        )
        
        self.plot_trade_analysis(
            os.path.join(self.output_dir, f"trade_analysis_{timestamp}.png")
        )
        
        # 生成绩效报告
        self.generate_performance_report(initial_cash)


if __name__ == "__main__":
    # 示例用法
    viz = PairsVisualization()
    
    # 查找最新的结果文件
    results_dir = "results"
    if os.path.exists(results_dir):
        files = os.listdir(results_dir)
        signals_files = [f for f in files if f.startswith("signals_") and f.endswith(".csv")]
        trades_files = [f for f in files if f.startswith("trades_") and f.endswith(".csv")]
        
        if signals_files and trades_files:
            # 使用最新的文件
            latest_signals = max(signals_files)
            latest_trades = max(trades_files)
            
            signals_path = os.path.join(results_dir, latest_signals)
            trades_path = os.path.join(results_dir, latest_trades)
            
            print(f"使用信号文件: {signals_path}")
            print(f"使用交易文件: {trades_path}")
            
            viz.create_all_visualizations(signals_path, trades_path)
        else:
            print("未找到结果文件，请先运行回测")
    else:
        print("结果目录不存在，请先运行回测")
