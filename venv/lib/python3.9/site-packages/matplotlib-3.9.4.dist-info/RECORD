__pycache__/pylab.cpython-39.pyc,,
matplotlib-3.9.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib-3.9.4.dist-info/LICENSE,sha256=WhqB6jAXKMi7opM9qDLAzWIina8giToCSrPVMkRGjbw,4830
matplotlib-3.9.4.dist-info/METADATA,sha256=ei7sI7VELRyseI8MJff12QoQybf3yGH-T685ZHK6oAI,11423
matplotlib-3.9.4.dist-info/RECORD,,
matplotlib-3.9.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib-3.9.4.dist-info/WHEEL,sha256=4QZuGDj4aQBwsmMfXTKCnISuNY_3YsAolc6TrDPmyB0,93
matplotlib/__init__.py,sha256=WeSzQt08wYslOiH6cuPHIqKTkC0Xw6sCeFTlH3QCTgw,53928
matplotlib/__init__.pyi,sha256=NPwMLxwar4wyMqXAMU_p_Wo3ea4xHJ1LrtZ_trNFFo8,2992
matplotlib/__pycache__/__init__.cpython-39.pyc,,
matplotlib/__pycache__/_afm.cpython-39.pyc,,
matplotlib/__pycache__/_animation_data.cpython-39.pyc,,
matplotlib/__pycache__/_blocking_input.cpython-39.pyc,,
matplotlib/__pycache__/_cm.cpython-39.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-39.pyc,,
matplotlib/__pycache__/_color_data.cpython-39.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-39.pyc,,
matplotlib/__pycache__/_docstring.cpython-39.pyc,,
matplotlib/__pycache__/_enums.cpython-39.pyc,,
matplotlib/__pycache__/_fontconfig_pattern.cpython-39.pyc,,
matplotlib/__pycache__/_internal_utils.cpython-39.pyc,,
matplotlib/__pycache__/_layoutgrid.cpython-39.pyc,,
matplotlib/__pycache__/_mathtext.cpython-39.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-39.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-39.pyc,,
matplotlib/__pycache__/_text_helpers.cpython-39.pyc,,
matplotlib/__pycache__/_tight_bbox.cpython-39.pyc,,
matplotlib/__pycache__/_tight_layout.cpython-39.pyc,,
matplotlib/__pycache__/_type1font.cpython-39.pyc,,
matplotlib/__pycache__/_version.cpython-39.pyc,,
matplotlib/__pycache__/animation.cpython-39.pyc,,
matplotlib/__pycache__/artist.cpython-39.pyc,,
matplotlib/__pycache__/axis.cpython-39.pyc,,
matplotlib/__pycache__/backend_bases.cpython-39.pyc,,
matplotlib/__pycache__/backend_managers.cpython-39.pyc,,
matplotlib/__pycache__/backend_tools.cpython-39.pyc,,
matplotlib/__pycache__/bezier.cpython-39.pyc,,
matplotlib/__pycache__/category.cpython-39.pyc,,
matplotlib/__pycache__/cbook.cpython-39.pyc,,
matplotlib/__pycache__/cm.cpython-39.pyc,,
matplotlib/__pycache__/collections.cpython-39.pyc,,
matplotlib/__pycache__/colorbar.cpython-39.pyc,,
matplotlib/__pycache__/colors.cpython-39.pyc,,
matplotlib/__pycache__/container.cpython-39.pyc,,
matplotlib/__pycache__/contour.cpython-39.pyc,,
matplotlib/__pycache__/dates.cpython-39.pyc,,
matplotlib/__pycache__/dviread.cpython-39.pyc,,
matplotlib/__pycache__/figure.cpython-39.pyc,,
matplotlib/__pycache__/font_manager.cpython-39.pyc,,
matplotlib/__pycache__/gridspec.cpython-39.pyc,,
matplotlib/__pycache__/hatch.cpython-39.pyc,,
matplotlib/__pycache__/image.cpython-39.pyc,,
matplotlib/__pycache__/layout_engine.cpython-39.pyc,,
matplotlib/__pycache__/legend.cpython-39.pyc,,
matplotlib/__pycache__/legend_handler.cpython-39.pyc,,
matplotlib/__pycache__/lines.cpython-39.pyc,,
matplotlib/__pycache__/markers.cpython-39.pyc,,
matplotlib/__pycache__/mathtext.cpython-39.pyc,,
matplotlib/__pycache__/mlab.cpython-39.pyc,,
matplotlib/__pycache__/offsetbox.cpython-39.pyc,,
matplotlib/__pycache__/patches.cpython-39.pyc,,
matplotlib/__pycache__/path.cpython-39.pyc,,
matplotlib/__pycache__/patheffects.cpython-39.pyc,,
matplotlib/__pycache__/pylab.cpython-39.pyc,,
matplotlib/__pycache__/pyplot.cpython-39.pyc,,
matplotlib/__pycache__/quiver.cpython-39.pyc,,
matplotlib/__pycache__/rcsetup.cpython-39.pyc,,
matplotlib/__pycache__/sankey.cpython-39.pyc,,
matplotlib/__pycache__/scale.cpython-39.pyc,,
matplotlib/__pycache__/spines.cpython-39.pyc,,
matplotlib/__pycache__/stackplot.cpython-39.pyc,,
matplotlib/__pycache__/streamplot.cpython-39.pyc,,
matplotlib/__pycache__/table.cpython-39.pyc,,
matplotlib/__pycache__/texmanager.cpython-39.pyc,,
matplotlib/__pycache__/text.cpython-39.pyc,,
matplotlib/__pycache__/textpath.cpython-39.pyc,,
matplotlib/__pycache__/ticker.cpython-39.pyc,,
matplotlib/__pycache__/transforms.cpython-39.pyc,,
matplotlib/__pycache__/typing.cpython-39.pyc,,
matplotlib/__pycache__/units.cpython-39.pyc,,
matplotlib/__pycache__/widgets.cpython-39.pyc,,
matplotlib/_afm.py,sha256=cWe1Ib37T6ZyHbR6_hPuzAjotMmi32y-kDB-i28iyqE,16692
matplotlib/_animation_data.py,sha256=JJJbbc-fMdPjkbQ7ng9BHL5i91VTDHQVTtEdWOvWBAI,7986
matplotlib/_api/__init__.py,sha256=FKsAM4RckCyJZiL-yXI1NRsvQxvbxxzSgvv8xpaX0I0,13304
matplotlib/_api/__init__.pyi,sha256=9xxTTkE9MIP4xFNwmRvM9j0QflFrqcFbowCAedGhdqo,2254
matplotlib/_api/__pycache__/__init__.cpython-39.pyc,,
matplotlib/_api/__pycache__/deprecation.cpython-39.pyc,,
matplotlib/_api/deprecation.py,sha256=F3IonykeAhU1z8N2ViqBEtPWWa1YDCYccmdl0CkgIEw,20027
matplotlib/_api/deprecation.pyi,sha256=a9djyVvnX2-t-IGrk3z1mrnYHcj4qYxRD0n9KDHWXlE,2208
matplotlib/_blocking_input.py,sha256=VHNsxvX2mTx_xBknd30MSicVlRXS4dCDe9hDctbV5rk,1224
matplotlib/_c_internal_utils.cpython-39-darwin.so,sha256=dWPkkv3lgfLwu-5wxJc1MfRxIqSFWvSIcOd7CT-O608,136352
matplotlib/_c_internal_utils.pyi,sha256=Z3bLs9pMGXrmZjt-4_A-x4321bLP-B54xDbr4PIgUfc,377
matplotlib/_cm.py,sha256=uxuEdcEkReRPOYpRCcRrCfsbl1N1T5l8QtnId3xnGUU,67190
matplotlib/_cm_listed.py,sha256=hpgMx7bjxJx5nl1PbQvaCDUBHQf8njaRrM2iMaBeZOM,109462
matplotlib/_color_data.py,sha256=k-wdTi6ArJxksqBfMT-7Uy2qWz8XX4Th5gsjf32CwmM,34780
matplotlib/_color_data.pyi,sha256=RdBRk01yuf3jYVlCwG351tIBCxehizkZMnKs9c8gnOw,170
matplotlib/_constrained_layout.py,sha256=zBx9pT8lIlOXrFb-K-8HSk_jjqFy-wwZsFf6z8SECnw,31003
matplotlib/_docstring.py,sha256=nPXtBXXO8doLznC_hkaf2OOiVZBQm-1TE-uA_KdtOAQ,3936
matplotlib/_docstring.pyi,sha256=YjWQjaiffjCBVQpNbI1lsuiUUBBD5HSSEOV2xKD1_KA,741
matplotlib/_enums.py,sha256=cq5dtb_qy4g3cHgr1KdVA9qzYalgz7KCtTytyFp3PAs,6474
matplotlib/_enums.pyi,sha256=B5MhNYWDyhVZteR5lo9uRJ8RiduSGKrYw1NkBTT8Mx4,364
matplotlib/_fontconfig_pattern.py,sha256=2livocARMbpys8tmicH6wlifcwcNoIstgruSY6DSAfk,4361
matplotlib/_image.cpython-39-darwin.so,sha256=qls4ydGj-91vDAauGCJ8e_7WtYzx6vUwhYvct1wX7zk,367032
matplotlib/_image.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_internal_utils.py,sha256=nhK6LLWYW93fBcsFiO09JmqFj2rgHEsGYFOeaC7HRKw,2140
matplotlib/_layoutgrid.py,sha256=mFB9asZVol2aV9hLBpdntuG5x1isKUrajF-3TUhCgso,21676
matplotlib/_mathtext.py,sha256=vVdbDoXC1lYioFO45cftsASoT4Re74rdREkUW0YNDfY,107860
matplotlib/_mathtext_data.py,sha256=Odf6FpGVC-ARfVIRokBl1qG_tvnAzWmCtdFICk0N9hc,65133
matplotlib/_path.cpython-39-darwin.so,sha256=hLElqghLOA3cFO0RqXqv2Q1n7QUY0l4S4bOD7DtxOOA,342288
matplotlib/_path.pyi,sha256=yznyfzoUogH9vvi0vK68ga4Shlbrn5UBhAnLX8Ght1o,325
matplotlib/_pylab_helpers.py,sha256=9EyocyQhnPvsSzIvbthznz6wlIa74i94WSnja1vKi0U,4306
matplotlib/_pylab_helpers.pyi,sha256=7OZKr-OL3ipVt1EDZ6e-tRwHASz-ijYfcIdlPczXhvQ,1012
matplotlib/_qhull.cpython-39-darwin.so,sha256=x5hA0FDHWbI7asNAQmTB2-1x64tLw4QhZI6Mq7hkMas,587824
matplotlib/_qhull.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_text_helpers.py,sha256=Eowfk0L2lBmYjMk2wCYJseoBCMR6odSM2f5xOTe6j7k,2543
matplotlib/_tight_bbox.py,sha256=ddJ5ViXulbPLocHr-RkWK27WJSuV2WXUx74jZyL0NOg,2787
matplotlib/_tight_layout.py,sha256=A3vZKBmxci7w35OO9lxgfUqrRKgTr-N_dUza0M05WxE,12675
matplotlib/_tri.cpython-39-darwin.so,sha256=Qci-FIi5-wCbAdwosPRNfW_UuThOOuPnPmNqCdUId4U,286176
matplotlib/_tri.pyi,sha256=yUtwM4BGi_KjvDY6CT8-Pafi8mfKLJtqLMmwK0xNJkQ,1026
matplotlib/_ttconv.cpython-39-darwin.so,sha256=UDUX88oNnuA4JHC6goKg8qjt2kDT4tbzoNO7XwrMB7g,169688
matplotlib/_ttconv.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_type1font.py,sha256=c_PXvyOFQ4YCRQJ23snWNkdEhuZ4k0JbLe55zaeoXfQ,28409
matplotlib/_version.py,sha256=XbkIFAFwsSJjopCLi4Ne4fIKWso2YG0VKjrtMTj1ZNc,18
matplotlib/animation.py,sha256=VSvfWiByVrNGEdwsLdQb3Bn0YXNjhbEamoK0ibTmsB0,71071
matplotlib/animation.pyi,sha256=C8Fgmswh4um-qYgqPZNBEpXVaefRXi6e026pYjonLHg,6566
matplotlib/artist.py,sha256=PkkMaP_yg33K8Yukf-p3mJcsF8CRPAtB8z2U3qfTnf4,63724
matplotlib/artist.pyi,sha256=cNZ0vUZYz6GjJ-CYe80M7koriHlCcvojmOn26qC6T6E,6870
matplotlib/axes/__init__.py,sha256=M5qLawRtk4NWxZ2Hf9ohCM7Lm4qvbMy77VFebKCvvsk,365
matplotlib/axes/__init__.pyi,sha256=HP1z2v-PboHQS4dQjvJ7XjUjX-zw6taZRTTB9oVKwYE,303
matplotlib/axes/__pycache__/__init__.cpython-39.pyc,,
matplotlib/axes/__pycache__/_axes.cpython-39.pyc,,
matplotlib/axes/__pycache__/_base.cpython-39.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-39.pyc,,
matplotlib/axes/_axes.py,sha256=4DnKP8g4s7U0uBlQPEynl35WfRYWsxRO6AuwGhIeMZk,342358
matplotlib/axes/_axes.pyi,sha256=V87WHlXhbMI7JuLpAkbGBrAhMpdy--2wXKJMzcJRYSw,25588
matplotlib/axes/_base.py,sha256=u3tSfBgYotdGMdK8IxxEAwTYV0IHIKrAdjEbMQD-2tc,182124
matplotlib/axes/_base.pyi,sha256=BLC_FyXh8F0Eu_XIuPum4CY_sqcFbJL1vc8USCzMIek,17010
matplotlib/axes/_secondary_axes.py,sha256=hg5ghWAcLe-XDgb66jaRnNau46fYW4nmeGKtANZ2Ygw,11867
matplotlib/axes/_secondary_axes.pyi,sha256=GtU55YzLNN7XHMRQcAmcAxcdcN-FzHw1RAJpOiTcZFk,1414
matplotlib/axis.py,sha256=9K96t3nvKMmR5CV_m2mmfYJ5fnnPXUkKWFI6uON_BWA,103108
matplotlib/axis.pyi,sha256=v15ue_f2ZQNEFDrjsmRWc7tLoGOjHQEgBIU3vqcmqoU,10156
matplotlib/backend_bases.py,sha256=tV4kREN0gxX9DFj6jM-G6HWs3DZhtnbdxnqyNwGkGfU,131593
matplotlib/backend_bases.pyi,sha256=SsMxDsEGxWCtuFlIG11x1CCQy04tvnaszDeUn_jlhAY,16216
matplotlib/backend_managers.py,sha256=RQheCO_cQBlaWsYMbAmswu0UPKU7bmLTI5LEFgotklA,11795
matplotlib/backend_managers.pyi,sha256=agnuM0wiZRqSgqti2AgbKJijRLvEPNLOrSY8PEwLjFE,2253
matplotlib/backend_tools.py,sha256=wXWJ5xLh7pCpbiGrLkJOeLAxE9f9_c0kdVTripfZaNY,33351
matplotlib/backend_tools.pyi,sha256=Pt07U2m84j7PPH_iim0mwZtf9BUhOlQC3kqAV6oOuNM,4030
matplotlib/backends/__init__.py,sha256=JowJe-tDrUBMNJTiJATgiEuACpgdxsKnRCYa-nC255A,206
matplotlib/backends/__pycache__/__init__.cpython-39.pyc,,
matplotlib/backends/__pycache__/_backend_gtk.cpython-39.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-39.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk4.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk4agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_gtk4cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qtagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_qtcairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-39.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-39.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-39.pyc,,
matplotlib/backends/__pycache__/registry.cpython-39.pyc,,
matplotlib/backends/_backend_agg.cpython-39-darwin.so,sha256=d6l7g6Iy37H2mXnjlwdfa35oYJrngPBeaNtslcMdTFA,256000
matplotlib/backends/_backend_agg.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/_backend_gtk.py,sha256=zNCmMOgjgZGfAIlxDU4ph02XMSSn0wIUoPQ4wsaeeAg,11274
matplotlib/backends/_backend_pdf_ps.py,sha256=E799e3XOJ5mp6YoecWp63xDS-DGqFLd4JfsGc0tMLRI,4444
matplotlib/backends/_backend_tk.py,sha256=zVr77hxmEutHx9obexgG_gROu2lMlZIkhNk387e6grE,42222
matplotlib/backends/_macosx.cpython-39-darwin.so,sha256=UOG9E2qCini-ia1VXmGXgz-YCNvkE_-RB7-3LKe-J7g,81384
matplotlib/backends/_macosx.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/_tkagg.cpython-39-darwin.so,sha256=ndD0H4b4YIn309Y1u9pB1Cgel4nfX7n2Oj9w_I981MU,156352
matplotlib/backends/_tkagg.pyi,sha256=1yKnYKSgNFesrb0ZI5RQCyTO4zONgHW5MrJ9vq4HzyI,379
matplotlib/backends/backend_agg.py,sha256=wkhS9W2QBjGdIYU27hDXJu-MXRh4dB0_itGMBjkGWfQ,20434
matplotlib/backends/backend_cairo.py,sha256=Ah5M85Ppa8zYGOtLOizW0gzltjs3b4nD56qFeQR795U,18618
matplotlib/backends/backend_gtk3.py,sha256=xhjmb15l_9dnlU5aLfEcbh6s16t5uopIZEEgJtOSANg,21644
matplotlib/backends/backend_gtk3agg.py,sha256=LzA-FwoCKrBIu4v-PJHuqx3HWK-4OM7wuujyyl8EWnI,2465
matplotlib/backends/backend_gtk3cairo.py,sha256=zPsJzVm750if2LiQ9ybzsbX0rBhkP05XVvR9Lhz65so,1392
matplotlib/backends/backend_gtk4.py,sha256=m-GqRG7YTsQhfIK2TOn5rii2LhnInW5M_Y-0kpNWWiY,21170
matplotlib/backends/backend_gtk4agg.py,sha256=00i3qpIt9Tcf_S74GOWbeckiPlfVJoQ2pBbhXDMthF0,1262
matplotlib/backends/backend_gtk4cairo.py,sha256=sqWm3WgfNO8EsBjqzsD4U4cAc0f4q5SufYx7ZJacDf0,1125
matplotlib/backends/backend_macosx.py,sha256=MYeU1HyVwdIotSYuNcu4c18_gOgX4AnCU061DIvw1X8,7373
matplotlib/backends/backend_mixed.py,sha256=PAYTjNuunpAa4-JkBguvyOjgDlB0eg9ARDAWidKfJpc,4698
matplotlib/backends/backend_nbagg.py,sha256=Au9RHfRufpI0ngT4R0K0CUVtAMFi9Bg-YhDunlj_Lko,8000
matplotlib/backends/backend_pdf.py,sha256=5Qxe_nG5JW3G9bZXaeab6CtOMeb8WNUmQjekWARk4iY,105846
matplotlib/backends/backend_pgf.py,sha256=_MZKA2sWAvoCIkz9YEuAxMPS5EaIkx41-mVpqQ-S54c,39660
matplotlib/backends/backend_ps.py,sha256=o43IOl5SEx_oBbO8EBX-ihlD-aYfsK9hIvSho5GWxTE,47465
matplotlib/backends/backend_qt.py,sha256=EEFQiRkGsc_Ja-RP22MorQZkT_qHJ_7Vrq9goiC_CuU,41321
matplotlib/backends/backend_qt5.py,sha256=kzfoo2ksEGsiWAa2LGtZYzKvfzqJJWyGOohohcRAu1g,787
matplotlib/backends/backend_qt5agg.py,sha256=Vh7H8kqWH4X8a3VX2XZ2Vze9srwJavkNHAZxdJUz_bk,352
matplotlib/backends/backend_qt5cairo.py,sha256=Go2Y0GVkXh1xh6x4F255_e5Xbwwws-OiD1Fc0805E78,292
matplotlib/backends/backend_qtagg.py,sha256=ZjPtp5wR6tZGjbngPXRdVXYRhiPPrc5C0q2DmtdRkpY,3413
matplotlib/backends/backend_qtcairo.py,sha256=e3SUG50VGqo68eS_8ebTCVQPa4AaxLxuo1JiWX4TIWg,1770
matplotlib/backends/backend_svg.py,sha256=2EH6dGrDBJvHmW0MC51-2q8Sbtj8iAs1SlpVpjHH-WU,50078
matplotlib/backends/backend_template.py,sha256=Z352VD5tp_xsNcR-DQcqt-LOB8lXoNzkCzFaMZaS0Dg,8010
matplotlib/backends/backend_tkagg.py,sha256=z9gB16fq2d-DUNpbeSDDLWaYmc0Jz3cDqNlBKhnQg0c,592
matplotlib/backends/backend_tkcairo.py,sha256=JaGGXh8Y5FwVZtgryIucN941Olf_Pn6f4Re7Vuxl1-c,845
matplotlib/backends/backend_webagg.py,sha256=YzNscpsID8yu0XYO3NXiZLMPZnDKMAKdG_TO2Ya3g4k,11022
matplotlib/backends/backend_webagg_core.py,sha256=7lcoinjD0fswQ1-acN5QOB1HJ6YoUgIZGrsmgZP5cl8,18303
matplotlib/backends/backend_wx.py,sha256=Ph53LEd5HcT_YtPV_udrG3k7xnOn0LA5t1VxdbpGIVQ,50585
matplotlib/backends/backend_wxagg.py,sha256=tzcwYyW34j4LPfHm9uhuHwepwZIcspi3y8oPC8FJkdk,1468
matplotlib/backends/backend_wxcairo.py,sha256=TK-m3S0c1WipfKE2IpIPNeE4hoXPjfMvnWAzHpCXpFs,848
matplotlib/backends/qt_compat.py,sha256=Ox7GVBb1BYvRLiyMt9w0qrisSA_Rqwq1BbEblSSYq2M,5340
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-39.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-39.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-39.pyc,,
matplotlib/backends/qt_editor/_formlayout.py,sha256=QmqqqLO6waqeSGOKjDNUwjvon53Z7yqil5AfqfftDWY,20953
matplotlib/backends/qt_editor/figureoptions.py,sha256=-0yrpqasVq2CtCw1gGnzPqQF6Zlj1H84krBo4AQ_RwE,9833
matplotlib/backends/registry.py,sha256=l6CsAe3Y25HZpkNjFOjduEuVlB_Y2VWX5oCBd_xqVTg,15708
matplotlib/backends/web_backend/all_figures.html,sha256=44Y-GvIJbNlqQaKSW3kwVKpTxBSG1WsdYz3ZmYHlUsA,1753
matplotlib/backends/web_backend/css/boilerplate.css,sha256=qui16QXRnQFNJDbcMasfH6KtN9hLjv8883U9cJmsVCE,2310
matplotlib/backends/web_backend/css/fbm.css,sha256=wa4vNkNv7fQ_TufjJjecFZEzPMR6W8x6uXJga_wQILw,1456
matplotlib/backends/web_backend/css/mpl.css,sha256=ruca_aA5kNnP-MZmLkriu8teVP1nIgwcFEpoB16j8Z4,1611
matplotlib/backends/web_backend/css/page.css,sha256=ca3nO3TaPw7865PN5SlGJBTc2H3rBXQCMaFPywX29y4,1623
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=wgSxUh3xpPAxOnZgSMnrhDM5hYncOfWRGgaCUezvedY,1311
matplotlib/backends/web_backend/js/mpl.js,sha256=nK4yXHi8DD8Oj0mlE_MZStFRH13-q67kFzlxpC_Sa-0,24376
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=Zs2Uzs7YUilG765nYvanCo-IK8HkHDtIum1KAq6bQ_w,302
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=F-By4ZjOSmwpNAkxUUxUk35qCjGlf0B28Z0aOyfpxDM,9514
matplotlib/backends/web_backend/single_figure.html,sha256=wEBwF602JLHErBUEXiS6jXqmxYAIzHpa3MMFrnev6rs,1357
matplotlib/bezier.py,sha256=DMqrzM6Rc5g99UiKXsppoYI5Da5awrzYDCvDBOJzbNM,19049
matplotlib/bezier.pyi,sha256=itubQt_U1IKnQ508bhy6J8LSpaspvZIeOmJqg7JvOmU,2586
matplotlib/category.py,sha256=aDGxoBkadhun-NDpc4clsA2oHS7Ehqj2g2ApnUoV3Ig,7316
matplotlib/cbook.py,sha256=x9ZWTaT6ke8vFfbGUMcZP7ofhqq5sgGKDNSfK35JlN4,79585
matplotlib/cbook.pyi,sha256=pq3CtksIt6AoYEH3WCbHKUv-M2fgkXRNbWOqxnXCbTM,6412
matplotlib/cm.py,sha256=N_v6hE1GTKeVqxR_Mfz5KyHeQNAMjCAIugnHAurXYFI,23745
matplotlib/cm.pyi,sha256=bgZU7a6K512niO9Rf7VqijDXqewCZZ3PKgsualbKTz4,2079
matplotlib/collections.py,sha256=sXK0PGHz162UlbWZbN7bNOiQbi00RvFZQkqLBL4gYxA,89127
matplotlib/collections.pyi,sha256=q0jLo4iym3IS_QaShIUf7g540vJFvN3M8vbMVFL2U6A,9993
matplotlib/colorbar.py,sha256=NS25-U7SbWw1LOfTWYht5HSy_0n9oMaJc_TDivUrgWc,60148
matplotlib/colorbar.pyi,sha256=lXMHYAvr9GnXcHgpy40WumUaNjX9TiHhpdWTpBwEl50,4800
matplotlib/colors.py,sha256=RdFmkCp7GaVo3L7FXOCEoNHZ41e-g6Xq8qrralWGMWY,102026
matplotlib/colors.pyi,sha256=W7mMeE_PF2jv4VvTCaq9EVyeuZoY6SoutNI4Ema-uq8,11192
matplotlib/container.py,sha256=Y6v4j79gMk8QDYfrdOqbbJHH0BoOIO9enz4dtlaBSJU,4565
matplotlib/container.pyi,sha256=DdthHVj1-bQQV3VcpMD1uIPLXVUKyKhWMXy8yCniK1I,1805
matplotlib/contour.py,sha256=O0EzECgbh-OmQ_6Hg0BRyoBfu8q6GHQypIN89DLd-U4,74733
matplotlib/contour.pyi,sha256=cco-D_g83ajEC0_B38ECTI47F49k-RxMCbcN6qduumA,5973
matplotlib/dates.py,sha256=fhN7zDua3xKIR-rbGdZMdANg0JZ2ry73rhfBPSr_hHc,66066
matplotlib/dviread.py,sha256=dcj4b6SLvOH1JucVIdoSQ50Tyngpfr-TT-WS5SgUu4k,41366
matplotlib/dviread.pyi,sha256=YZUFI3FQ2NDnodV7lCz9lAlsH0Yt0_mWMdZdmYOikHw,2197
matplotlib/figure.py,sha256=dttHRXxMNryvkZPTrn0JVXh_11opiQmqog-nc8NIlvs,137818
matplotlib/figure.pyi,sha256=CSPqZd36iQy-qbssqhCQizc40k6LabVJAvkfeJobEVA,14132
matplotlib/font_manager.py,sha256=5jDy5aRDIn0Gr2DZw_OGnNgVqwxJWHQvcR6VCV8xqGg,55298
matplotlib/font_manager.pyi,sha256=Jse1IshOdjhXHdjcvTxtos3rgsuECbRJzc264A-ZGus,5052
matplotlib/ft2font.cpython-39-darwin.so,sha256=FGvlvg1nDEJ_kKFbRi_A_ao9vd6gpha4zt3YmfGx_hs,757776
matplotlib/ft2font.pyi,sha256=4TEeRwvPFm2UyYdxx0z7fmJAdb7Jk_l7CFbP5FdqVms,7046
matplotlib/gridspec.py,sha256=3_3f_mqb8au9U2CAcSE-VbQRPr_QjpMhfSGE3uxJJVg,29765
matplotlib/gridspec.pyi,sha256=kGzdRdRag6vMMLXyB1JmMCULY9WX4acQaPb238h0yDw,5108
matplotlib/hatch.py,sha256=xAHggohTuCv3ErJdISdTgmZhxhAFYFs3Petrqw84V7M,7450
matplotlib/hatch.pyi,sha256=OEFkNe8TgrBli_MDMW99Tvhr44S4ySPaYQomjVXpUUA,2098
matplotlib/image.py,sha256=rqMDIAnHyjI3tAkkZXi_s0BTn2yfPwp9IIsJCtQEvb0,71694
matplotlib/image.pyi,sha256=y8hOWg1hQDm8r__6DlJUsX4_WvxA5uneilN9Th3s7_c,6812
matplotlib/layout_engine.py,sha256=NOsx76R_jx7_94n8OeSgqo58A1Kmlmpc-YH0GePcY68,11248
matplotlib/layout_engine.pyi,sha256=9cKFTFPCvwmKIygTUvrvOq1iWnFYme9Shniiv2QbC74,1788
matplotlib/legend.py,sha256=13dPH2R0FAKwUBcq-NbYdPbYmOo6dUPAYccN9iOg-Bg,56015
matplotlib/legend.pyi,sha256=hn-MNF3SPHtSUqIhwVXTebU_Nzk_wIh5iKgf7AEAZRg,5364
matplotlib/legend_handler.py,sha256=CxW3PlOMhpU6Wnp0sI0NVXomLph5haHfC1l9e-m2kvo,29915
matplotlib/legend_handler.pyi,sha256=3VEfeioGIAxhd3mhg4PXETZjCKf4OlXL0jz1MAFGtos,7655
matplotlib/lines.py,sha256=5V3KKUMqz9fRsg4RCKFZoYMs5L6VgUiULI4mdc5pONM,56050
matplotlib/lines.pyi,sha256=5RB6Fr5i67lRKqEiLNGIGRX5mtxT30nbt18WFrLsQao,6080
matplotlib/markers.py,sha256=g6ukerZ5n_sD_Enk0eJA4kkTvOOp8AfSGBq60PHot-E,33708
matplotlib/markers.pyi,sha256=FFFBsvilnbd8-5L04U70kfVoyBwc18w_fZ6DysTj9p0,1678
matplotlib/mathtext.py,sha256=FRczaMkP8Y17aSxlekQG1xdVs-51bxbo3afREoi0U2M,4943
matplotlib/mathtext.pyi,sha256=RCVxYGQ_CJ6wC7v_HkqoouU2PhtcvlJ1ffIyxzAC-so,1045
matplotlib/mlab.py,sha256=6iTxX5xyc4NEPpTzBQwE_CF6mwS4ShytML87lYtlcmA,30236
matplotlib/mlab.pyi,sha256=mkR7wbJS9eCQfCFsUWoXnqrAy0kcE3cVqpFZCeOGC_Q,3583
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=blR3ERmrVBV5XKkAnDCj4NMeYVgzH7cXtJ3u59u9GuE,12070
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=5qwEOpedEo76bDUahyuuF1q0cD84tRrX-VQ4p3MlfBo,10416
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=WDvgC_D3UkGJg9u-J0U6RaT02lF4oz3lQxHtg1r3lYw,10101
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=AbmzvCVWBceHRfmRfeJ9E6xzOQTFLk0U1zDfpf3_MaM,8295
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=4ji7_mTpeWMa93o_UHBWPKCnqsBfhJJNllat1lJArP4,6501
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=jjFrigwkTpYLqa26cpzZvKQNBo-PuF4bmDVqaM4pMWw,17183
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=sgNQdeYyx8J-itGw9h31y95aMBiTCRvmNSPTXwwS7xg,17255
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=ZUtfHPloNqcvGMHMxaKDSlshhOcjwheUx143RwpGdIU,17241
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=Yj1wBg6Jsqqz1KBfhRoJ3ACR-CMQol8Fj_ZM5NZ1gDk,17346
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=Zl5o6J_di9Y5j2EpHtjew-_sfg7-WoeVmO9PzOYSTUc,15157
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=JAOno930iTyfZILMf11vWtiaTgrJcPpP6FRTRhEMMD4,15278
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=UJqJjOJ6xQDgDBLX157mKpohIJFVmHM-N6x2-DiGv14,15000
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=AWislZ2hDbs0ox_qOWREugsbS8_8lpL48LPMR40qpi0,15181
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=6j1TS2Uc7DWSc-8l42TGDc1u0Fg8JspeWfxFayjUwi8,15352
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=smg3mjl9QaBDtQIt06ko5GvaxLsO9QtTvYANuE5hfG0,15422
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=7nxFr0Ehz4E5KG_zSE5SZOhxRH8MyfnCbw-7x5wu7tw,15339
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=NKEz7XtdFkh9cA8MvY-S3UOZlV2Y_J3tMEWFFxj7QSg,15443
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=NAx4M4HjL7vANCJbc-tk04Vkol-T0oaXeQ3T2h-XUvM,17155
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=8e_myD-AQkNF7q9XNLb2m76_lX2TUr3a5wog_LIE1sk,17086
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=8fkBRmJ-SWY2YrBg8fFyjJyrJp8daQ6JPO6LvhM8xPI,17230
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=aeVRvV4r15BBvxuRJ0MG8ZHuH2HViuIiCYkvuapmkmM,17195
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=IyMYM-bgl-gI6rG0EuZZ2OLzlxJfGeSh8xqsh0t-eJQ,15627
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=s12C-eNnIDHJ_UVbuiprjxBjCiHIbS3Y8ORTC-qTpuI,15729
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=Kt8KaRidts89EBIK29X2JomDUEDxvroeaJz_RNTi6r4,17839
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=lL5fAHTRwODl-sB5mH7IfsD1tnnea4yRUK-_Ca2bQHM,17781
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=3KqK3eejiR4hIFBUynuSX_4lMdE2V2T58xOF8lX-fwc,17919
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Vx9rRf3YfasMY7tz-njSxz67xHKk-fNkN7yBi0X2IP0,17877
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=aoXepTcDQtQa_mspflMJkEFKefzXHoyjz6ioJVI0YNc,16028
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=pCWW1MYgy0EmvwaYsaYJaAI_LfrsKmDANHu7Pk0RaiU,17496
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=0CIB2BLe9r-6_Wl5ObRTTf98UOrezmGQ8ZOuBX5kLks,16665
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=5R-pLZOnaHNG8pjV6MP3Ai-d2OTQYR_cYCb5zQhzfSU,16920
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=3EzUbNnXr5Ft5eFLY00W9oWu59rHORgDXUuJaOoKN58,15662
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=X_9tVspvrcMer3OS8qvdwjFFqpAXYZneyCL2NHA902g,15810
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=ijMb497FDJ9nVdVMb21F7W3-cu9sb_9nF0oriFpSn8k,15752
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=8KITbarcUUMi_hdoRLLmNHtlqs0TtOSKqtPFft7X5nY,15733
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=Iyt8ajE4B2Tm34oBj2pKtctIf9kPfq05suQefq8p3Ro,9644
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=bL1fA1NC4_nW14Zrnxz4nHlXJb4dzELJPvodqKnYeMg,17983
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=-_Ui6XlKaFTHEnkoS_-1GtIr5VtGa3gFQ2ezLOYHs08,18070
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=IEcsWcmzJyjCwkgsw4o6hIMmzlyXUglJat9s1PZNnEU,17942
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=49fQMg5fIGguZ7rgc_2styMK55Pv5bPTs7wCzqpcGpk,18068
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=qMaHTdpkrNL-m4DWhjpxJCSmgYkCv1qIzLlFfM0rl40,21532
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=g7AVJyiTxeMpNk_1cSfmYgM09uNUfPlZyWGv3D1vcAk,21931
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=XYmNC5GQgSVAZKTIYdYeNksE6znNm9GF_0SmQlriqx0,22148
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=i7fVe-iLyLtQxCfAa4IxdxH-ufcHmMk7hbCGG5TxAY4,21891
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=wyuoIWEZOcoXrSl1tPzLkEahik7kGi91JJj-tkFRG4A,16250
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=MyjLAnzKYRdQBfof1W3k_hf30MvqOkqL__G22mQ5xww,9467
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=sIDDI-B82VZ3C0mI_mHFITCZ7PVn37AIYMv1CrHX4sE,15333
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=zg61QobD3YU9UBfCXmvmhBNaFKno-xj8sY0b2RpgfLw,15399
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=vRQm5j1sTUN4hicT1PcVZ9P9DTTUHhEzfPXqUUzVZhE,15441
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Mdcq2teZEBJrIqVXnsnhee7oZnTs6-P8_292kWGTrw4,15335
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=i2l4gcjuYXoXf28uK7yIVwuf0rnw6J7PwPVQeHj5iPw,69269
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=Um5O6qK11DXLt8uj_0IoWkc84TKqHK3bObSKUswQqvY,69365
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=hVYDg2b52kqtbVeCzmiv25bW1yYdpkZS-LXlGREN2Rs,74392
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=23cvKDD7bQAJB3kdjSahJSTZaUOppznlIO6FXGslyW8,74292
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=P5UaoXr4y0qh4SiMa5uqijDT6ZDr2-jPmj1ayry593E,9740
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=cQTmr2LFPwKQE_sGQageMcmFicjye16mKJslsJLHQyE,64251
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=pzWOdycm6RqocBWgAVY5Jq0z3Fp7LuqWgLNMx4q6OFw,59642
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=bK5puSMpGT_YUILwyJrXoxjfj7XJOdfv5TQ_iKsJRzw,66328
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=hhNrUnpazuDDKD1WpraPxqPWCYLrO7D7bMVOg-zI13o,60460
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=ZuOmt9GcKofjdOq8kqhPhtAIhOwkL2rTJTmZxAjFakA,9527
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=MRv8ppSITYYAb7lt5EOw9DWWNZIblfxsFhu5TQE7cpI,828
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=11k43sCY8G8Kw8AIUwZdlPAgvhw8Yu8dwpdboVtNmw4,4816
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=urPTHf7wf0g2JPL2XycR52BluOcnMnixwHHt4QQcmVk,5476
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back-symbolic.svg,sha256=Okj_ressZkfe6Ewv_o7GF5toc5qWCeFkQ2cHQ25BdVE,1532
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=Okj_ressZkfe6Ewv_o7GF5toc5qWCeFkQ2cHQ25BdVE,1532
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave-symbolic.svg,sha256=dMGXvLSOHPu44kiWgZx-B_My_tLWaP6J6GgxJfL4FW0,2049
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=dMGXvLSOHPu44kiWgZx-B_My_tLWaP6J6GgxJfL4FW0,2049
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward-symbolic.svg,sha256=kOiKq3a4mieMRLVCwQBdOMTRrWG2NOX_5-rbAFHpdmQ,1551
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=kOiKq3a4mieMRLVCwQBdOMTRrWG2NOX_5-rbAFHpdmQ,1551
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=hxxBtakaVFA7mpZOGakvo0QUcb2x06rojeS5gnVmyuc,4906
matplotlib/mpl-data/images/help-symbolic.svg,sha256=XVcFcuzcL3SQ3LjfSbtdLYDjoB5YUkj2jk2Gk8vaZF8,1890
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.svg,sha256=XVcFcuzcL3SQ3LjfSbtdLYDjoB5YUkj2jk2Gk8vaZF8,1890
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/home-symbolic.svg,sha256=ptrus8h5PZTi9ahYfnaz-uZ8MAHCr72aPeMW48TBR9Q,1911
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=ptrus8h5PZTi9ahYfnaz-uZ8MAHCr72aPeMW48TBR9Q,1911
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move-symbolic.svg,sha256=_uamLnjQ20iwSuKbd8JvTXUFaRq4206MrpFWvtErr8I,2529
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_uamLnjQ20iwSuKbd8JvTXUFaRq4206MrpFWvtErr8I,2529
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=sdrNIxYT-BLvJ30ASnaRQ5PxF3SB41-pgdaIJT0KqBg,1264
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots-symbolic.svg,sha256=Gq4fDSS99Rv5rbR8_nenV6jcY5VsKPARWeH-BZBk9CU,2150
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=Gq4fDSS99Rv5rbR8_nenV6jcY5VsKPARWeH-BZBk9CU,2150
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg,sha256=uMmdGkO43ZHlezkpieR3_MiqlEc5vROffRDOhY4sxm4,1499
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=uMmdGkO43ZHlezkpieR3_MiqlEc5vROffRDOhY4sxm4,1499
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/kpsewhich.lua,sha256=RdyYaBnBLy3NsB5c2R5FGrKu-V-WBcZim24NWilsTfw,139
matplotlib/mpl-data/matplotlibrc,sha256=wlUqhpUjmDxJ3C9QmmYZiUyDuRGo9lWyNGidWJKogFM,42364
matplotlib/mpl-data/plot_directive/plot_directive.css,sha256=utSJ1oETz0UG6AC9hU134J_JY78ENijqMZXN0JMBUfk,318
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/README.txt,sha256=ABz19VBKfGewdY39QInG9Qccgn1MTYV3bT5Ph7TCy2Y,128
matplotlib/mpl-data/sample_data/Stocks.csv,sha256=72878aZNXGxd5wLvFUw_rnj-nfg4gqtrucZji-w830c,67924
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=A0SU3buOUGhT-NI_6LQ6p70fFSIU3iLFdgzvzrKR6SE,132
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=cUqVw5vDHNSZoaO4J0ebZUf5SrJP36775abs7R9Bclg,2186
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=DXNx4FXeyqxHy26AmvNELpwezQLxweLQY9HP7ktKIdc,22279
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=GArKb0O3DgKZRsKdJf6lX3rMSf-PCekIiBoLNdgF7Mk,3211
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=aytOm4eT_SPvs7HC28ZY4GukeN44q-SE0JEMCR8kVOk,1257
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=iopHpMaM3im_AK2aiHGuM2DKM5i9Kc84v6NQEoSb10Q,167
matplotlib/mpl-data/stylelib/_mpl-gallery-nogrid.mplstyle,sha256=1VOL3USqD6iuGQaSynNg1QhyUwvKLnkLyUKdbBMnnqg,489
matplotlib/mpl-data/stylelib/_mpl-gallery.mplstyle,sha256=MN-q59CiDqHXB8xFKXxzCbJJbJmNDhBe9lDJJAoMTPA,504
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=-KbhaI859BITHIoyUZIfpQDjfckgLAlDAS_ydKsm6mc,712
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=tGkwXsHm15yZij7HT_9N8L9_z8-D_ao2wUWtimVikGA,24693
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=Ht6phZUy3zNRdcfHKcSb1uh3O8DunSPX8HPt9xTyzuo,658
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=yTa2YEIIP9xi5V_G0p2vSlxghuhNwjRi9gPECMxyRiM,288
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=WNUmAFuBPcqQPVgt6AS1ldy8Be2XO01N-1YQL__Q6ZY,832
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=u2oPHMLWFtZcpIjHk2swi2Nrt4NgnEtof5lxcwM0RD0,956
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=KCLg-pXpns9cnKDXKN2WH6mV41OH-6cbT-5zKQotSdw,526
matplotlib/mpl-data/stylelib/seaborn-v0_8-bright.mplstyle,sha256=pDqn3-NUyVLvlfkYs8n8HzNZvmslVMChkeH-HtZuJIc,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-colorblind.mplstyle,sha256=eCSzFj5_2vR6n5qu1rHE46wvSVGZcdVqz85ov40ZsH8,148
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark-palette.mplstyle,sha256=p5ABKNQHRG7bk4HXqMQrRBjDlxGAo3RCXHdQmP7g-Ng,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark.mplstyle,sha256=I4xQ75vE5_9X4k0cNDiqhhnF3OcrZ2xlPX8Ll7OCkoE,667
matplotlib/mpl-data/stylelib/seaborn-v0_8-darkgrid.mplstyle,sha256=2bXOSzS5gmPzRBrRmzVWyhg_7ZaBRQ6t_-O-cRuyZoA,670
matplotlib/mpl-data/stylelib/seaborn-v0_8-deep.mplstyle,sha256=44dLcXjjRgR-6yaopgGRInaVgz3jk8VJVQTbBIcxRB0,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-muted.mplstyle,sha256=T4o3jvqKD_ImXDkp66XFOV_xrBVFUolJU34JDFk1Xkk,143
matplotlib/mpl-data/stylelib/seaborn-v0_8-notebook.mplstyle,sha256=PcvZQbYrDdducrNlavBPmQ1g2minio_9GkUUFRdgtoM,382
matplotlib/mpl-data/stylelib/seaborn-v0_8-paper.mplstyle,sha256=n0mboUp2C4Usq2j6tNWcu4TZ_YT4-kKgrYO0t-rz1yw,393
matplotlib/mpl-data/stylelib/seaborn-v0_8-pastel.mplstyle,sha256=8nV8qRpbUrnFZeyE6VcQ1oRuZPLil2W74M2U37DNMOE,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-poster.mplstyle,sha256=dUaKqTE4MRfUq2rWVXbbou7kzD7Z9PE9Ko8aXLza8JA,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-talk.mplstyle,sha256=7FnBaBEdWBbncTm6_ER-EQVa_bZgU7dncgez-ez8R74,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-ticks.mplstyle,sha256=CITZmZFUFp40MK2Oz8tI8a7WRoCizQU9Z4J172YWfWw,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-white.mplstyle,sha256=WjJ6LEU6rlCwUugToawciAbKP9oERFHr9rfFlUrdTx0,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-whitegrid.mplstyle,sha256=ec4BjsNzmOvHptcJ3mdPxULF3S1_U1EUocuqfIpw-Nk,664
matplotlib/mpl-data/stylelib/seaborn-v0_8.mplstyle,sha256=_Xu6qXKzi4b3GymCOB1b1-ykKTQ8xhDliZ8ezHGTiAs,1130
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=BsirZVd1LmPWT4tBIz6loZPjZcInoQrIGfC7rvzqmJw,190
matplotlib/offsetbox.py,sha256=OyAGOJ7LPT1X5CAyZwuIS1W3l-OKPWzpSF1UxCF3voU,53178
matplotlib/offsetbox.pyi,sha256=qtRlo1OQQ1eUSRwzvHT8EWa8a2YQ54b9j1YZwxpo0AQ,10390
matplotlib/patches.py,sha256=sfawmLPt_cpMZ-URNub4_COlgJFrxRFA5goJhg3_HcE,162832
matplotlib/patches.pyi,sha256=OvxnwlppjOnWDTAu17y4qIYnk8G2lEVh19SrsJweW8U,22543
matplotlib/path.py,sha256=N87A45qgGW337KOriFkNSqjGhC7ZT0B9I5MDBbB2ebo,42079
matplotlib/path.pyi,sha256=N1XOCWSUG4RtCcMo02yvvq3HDYo_369oKQkEJUHN80c,4777
matplotlib/patheffects.py,sha256=s7J8EiS3i110JYSgQdoH16xaNHMf9xnUhj9SkJjwSmQ,18789
matplotlib/patheffects.pyi,sha256=7-FhuxGGrer94GtJ1sZ0YxOmK6Nv4oixTmsKb6-ijOg,3664
matplotlib/projections/__init__.py,sha256=jq1SJYHwkgHKHA-c_4r9j-dV2ZpgmUkWZnCOVNPtmKo,4436
matplotlib/projections/__init__.pyi,sha256=D28dSYmwZcSBFBtNDer-QqE_lqXAhHKersuAlvi89jE,673
matplotlib/projections/__pycache__/__init__.cpython-39.pyc,,
matplotlib/projections/__pycache__/geo.cpython-39.pyc,,
matplotlib/projections/__pycache__/polar.cpython-39.pyc,,
matplotlib/projections/geo.py,sha256=inOLKvVTfZUTC-1Wl0Rq9eXouplbcVbWptkRVsFTkj8,17711
matplotlib/projections/geo.pyi,sha256=vPfhvj7_e0ZnKjyfDUNC89RGCktycJBPnn5D8w0A7N8,3775
matplotlib/projections/polar.py,sha256=d448pT7GPT0ukQTsr97NkPNtjQwC4ypDUJsPePEuj3w,56568
matplotlib/projections/polar.pyi,sha256=IWNSLQIo5cXFBJur5VFVDQBwAtq5n1XFZMnrj85mCBg,6636
matplotlib/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/pylab.py,sha256=VqUqd2J2-dKtltZtsYP8ePKoX1jsNDpTyqlcmpfC-lo,2369
matplotlib/pyplot.py,sha256=ydE449Dwz5FMgmx18EC7O_PnNJvDZJPxExAsK8BR_vs,147190
matplotlib/quiver.py,sha256=YDCU2XRe2VQhXo-LdAsM6Aa5doSIOikWfgLFYT-i4no,46177
matplotlib/quiver.pyi,sha256=7WC5M42icWeWVzOvDJVNdZ3NC4KvZz1M38hXeNRd1dE,5581
matplotlib/rcsetup.py,sha256=St4wrlo-NAy8iIZxTNl4cQSyJB6ejeW6QYi7P1K-BFM,51836
matplotlib/rcsetup.pyi,sha256=0VTIhzfKgBcKxOeOdoKq1oa7ZubalOj9Ks0efE0-cms,4337
matplotlib/sankey.py,sha256=qFEkTvL15SL0_vIOLldCyb5YsYniUzFnXs09kDuNmI0,36158
matplotlib/sankey.pyi,sha256=xa6EMuSEZQYycZWdqlxIgWqQ7gfWKPKF9ZDK7WYQAC0,1462
matplotlib/scale.py,sha256=GZKWo9toXXvkbxS28D7pMU2Fms-F4eR2RUCIjesKk1Q,25990
matplotlib/scale.pyi,sha256=-ptRptcqiAuzfKwrjgSWWOxFmjRUTOKGwIoWtuBXKgY,5057
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/__pycache__/__init__.cpython-39.pyc,,
matplotlib/sphinxext/__pycache__/figmpl_directive.cpython-39.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-39.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-39.pyc,,
matplotlib/sphinxext/__pycache__/roles.cpython-39.pyc,,
matplotlib/sphinxext/figmpl_directive.py,sha256=GaIho4pRZ9f0vKx9g6BSj64AF2BdRWV6Z_uzamCytWs,9118
matplotlib/sphinxext/mathmpl.py,sha256=R24VAgKSOkYx2sarr2MGHgsosvKopih6JEIu8VM65wc,7810
matplotlib/sphinxext/plot_directive.py,sha256=6eoNy8lyELEpjYgUvrAkjEqDOc4-5ndQ-zjpF4gKQ74,32430
matplotlib/sphinxext/roles.py,sha256=U7P4t06foLF2PsZJrTN4mfaspKGRoYwFZ3ijIhIUVyo,4882
matplotlib/spines.py,sha256=TM77Hz0LzJtp-12RTiykKNenIp_vwzrvkGooxMK6Upc,21593
matplotlib/spines.pyi,sha256=QXWS95dKxHbwzrtky-lrd4bXuj7eiz8mv54qIrMd0Ks,2956
matplotlib/stackplot.py,sha256=G4sylNIsZpJkWvbc4bdYdlyidicZFFB7e5J2jOtYUwU,4968
matplotlib/stackplot.pyi,sha256=311zv4wPimk9_GJdr_sfu1laNZRpKwsZ8pLMq16v8UQ,536
matplotlib/streamplot.py,sha256=rXVKYEi4MfSg3HnAaWb8w4qFp95mRf6dosMVqtXZEpE,24011
matplotlib/streamplot.pyi,sha256=wA0K5e_ZjXneS5WzonHxoKPLla3-IdflAcIrB0svtOY,2664
matplotlib/style/__init__.py,sha256=fraQtyBC3TY2ZTsLdxL7zNs9tJYZtje7tbiqEf3M56M,140
matplotlib/style/__pycache__/__init__.cpython-39.pyc,,
matplotlib/style/__pycache__/core.cpython-39.pyc,,
matplotlib/style/core.py,sha256=P67uPZwbLqggEHTpKTc17G20QeUl00P7AFSONf77L2g,8609
matplotlib/style/core.pyi,sha256=DElPp0fbkwKaaVOVb5VGHfI8ohRB9m4uVGKjZl3A65E,449
matplotlib/table.py,sha256=Q76nxe9lf-izvLsSE21PMyRImRWUehA3U-SfQnwj8x0,27161
matplotlib/table.pyi,sha256=AMQswiPK8WZ8NATDqy5GtwAlvwR5qStRFDYL13gxRYg,3041
matplotlib/testing/__init__.py,sha256=pTaqH1qgGNBeWCmvK_vjqFJXJ9dvZXCaKQ-8jHSpaEc,6942
matplotlib/testing/__init__.pyi,sha256=ffqfetWzyCVrSx7BlnoxCmbgIaZg2x57nDrq9eucRk0,1752
matplotlib/testing/__pycache__/__init__.cpython-39.pyc,,
matplotlib/testing/__pycache__/_markers.cpython-39.pyc,,
matplotlib/testing/__pycache__/compare.cpython-39.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-39.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-39.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-39.pyc,,
matplotlib/testing/__pycache__/widgets.cpython-39.pyc,,
matplotlib/testing/_markers.py,sha256=0iNyOi25XLv_gTfSUqiRizdSqJzozePPBMRo72H2Je4,1419
matplotlib/testing/compare.py,sha256=d4lEAmi-C0rCxLrELwfiQezOthqEgnhvuKeIskYim0Q,19332
matplotlib/testing/compare.pyi,sha256=xlJ4chgXKe567NUavlu-dalyPr4wAQUbd2Fz6aK_JII,1192
matplotlib/testing/conftest.py,sha256=-boEzYRlszzHJ2T9ZJq5qu8B38BlJ2hNpkzpIRGCSsA,3669
matplotlib/testing/conftest.pyi,sha256=4_MStQN6gy_h0YfhlPbqR81QVeVcn-FRqaKyyXedUEk,332
matplotlib/testing/decorators.py,sha256=viNGSCIQ9Ul_wjMCjrm-ebC_BClwU1Zkhxww6dq1m2k,18021
matplotlib/testing/decorators.pyi,sha256=0fSpdLBtEH7ZP_trVJ7RPxNtOX9sJ_z-MkNsbUxF8nM,872
matplotlib/testing/exceptions.py,sha256=72QmjiHG7DwxSvlJf8mei-hRit5AH3NKh0-osBo4YbY,138
matplotlib/testing/jpl_units/Duration.py,sha256=9FMBu9uj6orCWtf23cf6_9HCFUC50xAHrCzaxATwQfM,3966
matplotlib/testing/jpl_units/Epoch.py,sha256=-FGxeq-VvCS9GVPwOEE5ind_G4Tl9ztD-gYcW9CWzjo,6100
matplotlib/testing/jpl_units/EpochConverter.py,sha256=fhWjyP567bzcTU_oNuJJpucoolqS88Nt-yEFg1-3yEk,2944
matplotlib/testing/jpl_units/StrConverter.py,sha256=codGw9b_Zc-MG_YK4CiyMrnMR8ahR9hw836O2SsV8QI,2865
matplotlib/testing/jpl_units/UnitDbl.py,sha256=EABjyEK4MVouyvlwi_9KdYDg-qbYY3aLHoUjRw37Fb0,5882
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=B8DssrQVyC4mwvSFP78cGL0vCnZgVhDaAbZE-jsXLUg,2828
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=246hgA4_pCfJm-P94hEsxqnTS9t0XlvLC8p1v_bw2pU,657
matplotlib/testing/jpl_units/__init__.py,sha256=p__9RUwrt2LJ2eoT2JPM-42XLxSJrfA4az3rN5uP6d4,2684
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-39.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-39.pyc,,
matplotlib/testing/widgets.py,sha256=d5wWO4ulIBFj0ecOiuxqTTDInuYI6-1MSQp0SX9HG7c,3471
matplotlib/testing/widgets.pyi,sha256=Ioau7Q2aPRDZLx8hze2DOe3E1vn7QPxePC74WMR7tFc,831
matplotlib/tests/__init__.py,sha256=XyXveEAxafB87gnbx0jkC0MggzKO8FvORq_6RtJRwo4,366
matplotlib/tests/__pycache__/__init__.cpython-39.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_api.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_axis.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_gtk3.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_macosx.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_template.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_datetime.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_doc.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_ft2font.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_getattr.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_polar.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_textpath.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_ttconv.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-39.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-39.pyc,,
matplotlib/tests/conftest.py,sha256=HNR8xF4YiUoKY03WKoeABGwSRcKFMwI9cNFXZJDzz-A,119
matplotlib/tests/test_afm.py,sha256=A7jm2o-QaQH9SSpiFxGtZkbVU0LJZE69jfPv7RczOD4,3701
matplotlib/tests/test_agg.py,sha256=BwRu6BuTWPZYsc2YxcfllV9cvZ7WPbCe36Alhmkr-6k,10740
matplotlib/tests/test_agg_filter.py,sha256=3c_Smtb4OHEOfdMFCOb2qKhzMXSbNoaUtsJ0pW47Q44,1067
matplotlib/tests/test_animation.py,sha256=gwxe9pC1Jv00TjusWvgYiX32E1xMxGejSRkwqAoxuPY,17743
matplotlib/tests/test_api.py,sha256=fTw5hpYH_N_2YeoYcKJwLwFwn4LXDAOy-ZgvyWwU6x4,3503
matplotlib/tests/test_arrow_patches.py,sha256=wzczYyoF_YWuj5DjbNJ7iyAfWSkMXPYB1AjIoEEqoCM,6571
matplotlib/tests/test_artist.py,sha256=9ZQ82ZP4PKIuzucDD45phJZBDnPJkdbcPeHzrGNhHF8,17433
matplotlib/tests/test_axes.py,sha256=bipNAFvOLTz7gDKfhSBlFNh7A_nB35TvSQODjzdxawU,306431
matplotlib/tests/test_axis.py,sha256=ecaZ66ng6NkwrffxyEug9GzEx4YouY49xamqyvrJ0kA,268
matplotlib/tests/test_backend_bases.py,sha256=CDYfkBSD9xji68S6ytx5pSg1QwF3RGLOv5WSZW6lS1Q,22631
matplotlib/tests/test_backend_cairo.py,sha256=O2LTYjsfPn__bKtTz4MGGBodpSshoPkzu0INsc18xmI,1821
matplotlib/tests/test_backend_gtk3.py,sha256=6i9wimlBue9Tb-i_UyjMt_MdP9Z-SoOCXy5Tz64hK3Q,1739
matplotlib/tests/test_backend_macosx.py,sha256=IgWMoxDqDP9du5w8iRrDygHgiuCQi4EgEsPXpVw3DWA,1723
matplotlib/tests/test_backend_nbagg.py,sha256=o6ON7tt_LOW7wzg4StIMYuTgMFSPNWt1FOv2FFMIefk,1459
matplotlib/tests/test_backend_pdf.py,sha256=G9ppQYISSVHpyEHShmzP-ItYc4W8wq2KnyZ8LWh9JYE,15145
matplotlib/tests/test_backend_pgf.py,sha256=ai6Pr61a4_Dx826wo_vJGXYxI1N7ZtzBbx0TIMnwHog,13230
matplotlib/tests/test_backend_ps.py,sha256=SxQYqNKDFNOhtifKw02qx95LeHFOcCiHZIbaNGsEMH8,12572
matplotlib/tests/test_backend_qt.py,sha256=A1LntIOeismjmqels6bKt9LOiT9Z4lS-pxo1MG0Rw2I,12350
matplotlib/tests/test_backend_svg.py,sha256=zChB85qffPQ9KmFQQ1OCZH0F3GhqVuZiu8Ac_MjXADw,21053
matplotlib/tests/test_backend_template.py,sha256=uuE7oZ9pSBVDWrPL05B0WgCFsgv6HlXyetuPTfJn6a8,2184
matplotlib/tests/test_backend_tk.py,sha256=kQ3R4TzI2fH-qs391q6zaD9pxMXgZ9zkxfS_hI_X5cI,8282
matplotlib/tests/test_backend_tools.py,sha256=C-B7NCkyWsQ5KzQEnI5Be16DsAHHZJU9P5v9--wsF-o,501
matplotlib/tests/test_backend_webagg.py,sha256=4Oc-z7w-mTGETB1x0FQ_gZP9qHfyWh5lwWc9qPkkisc,938
matplotlib/tests/test_backends_interactive.py,sha256=1sT0tIjmlCOYOyOaAuBsCqMC-VlFbCoxyT0wausy6lw,28701
matplotlib/tests/test_basic.py,sha256=8XmkUXa8LgZO-BXqo5AusB4beRBVBKl52kFdOrS_2Po,1133
matplotlib/tests/test_bbox_tight.py,sha256=y08219QyXHk1KINciiFLiMIm4xVbCfhLe1Ch9rpV7jE,6313
matplotlib/tests/test_category.py,sha256=S6Y681G-wU1R10GCdtDMmR6fbVGURSLzFOWu0SNkON8,11700
matplotlib/tests/test_cbook.py,sha256=cnygK9Dotacl5IOHFCwVxkJ3wAZJ3RkZb3mNewSXyRg,31419
matplotlib/tests/test_collections.py,sha256=dBXU5e_J582wAWrVoIUVaE3Jmu6H1z3umMcJejWk7Y4,46695
matplotlib/tests/test_colorbar.py,sha256=EJKhhIA79JmAi2D9vAh3PK6Uujdvrm16usb4oGhPdqc,46648
matplotlib/tests/test_colors.py,sha256=4MejF3jWuDTad8kL9NeIgvbT4-ysTF1MhL1f2Rwo070,60244
matplotlib/tests/test_compare_images.py,sha256=NcBoT8HAxtzoR1zZBu0V3MFpGWtdFaDblQ8o3OTO6zM,3260
matplotlib/tests/test_constrainedlayout.py,sha256=hQml8aHHoma6stcX0tUnWxeKbhHfma3ExJEoRY_zwc4,22737
matplotlib/tests/test_container.py,sha256=FTewckOd3dJqLOzEUa29Itjqusk7Mx7MK5xPVMhzMmc,694
matplotlib/tests/test_contour.py,sha256=29G1GzKbxSE8IZ2ewZOqFr0EbpuyczguA3rsYv4pztA,33252
matplotlib/tests/test_cycles.py,sha256=3KyRmWH29WUgvIXUT06tKVDNCfDWqxuxlLueIp-FIl0,5996
matplotlib/tests/test_dates.py,sha256=l9LbiYzmF-zlvwD2QdGBbWGgJNF94gRHhsYKLzIjI18,55944
matplotlib/tests/test_datetime.py,sha256=_sh6R405rrZHce-WgH9GLcjLHXwTLpIPvVUs7BUQaCM,32621
matplotlib/tests/test_determinism.py,sha256=gsz-JIDkSBev0Mm-bE1jxvsh1V1UJ_lRK-QCVeO-P_s,4626
matplotlib/tests/test_doc.py,sha256=K6HhdRcHRUNNC0iIhxBLCq5tOqxqHBSvanHxXg5w5iI,1015
matplotlib/tests/test_dviread.py,sha256=JeTuA2FMUj1FddxDVBXUtnvZYTgztE-CyRXL_mI20P0,2764
matplotlib/tests/test_figure.py,sha256=9sUvk9A5VZQivMeiphTsrmm3g9eDuuiojtO-1-awSzE,58430
matplotlib/tests/test_font_manager.py,sha256=tHeeojRH-zC2fIcSziFi-j_nzPDm3qbPdUl9CGOrNek,12222
matplotlib/tests/test_fontconfig_pattern.py,sha256=LSR6lWF_0cVOshkGiflYaTCLcISRIJM6mjXm5QtDjX4,2168
matplotlib/tests/test_ft2font.py,sha256=vvc6OOHPkVDwTaOQVP2kt9H1_scFZeOWBTb8zdSrVn8,4469
matplotlib/tests/test_getattr.py,sha256=Tl_H1zpwLdSIVutc4vi-QwDCeWPzBGpN31N9ItzTkeQ,1090
matplotlib/tests/test_gridspec.py,sha256=SYJuJucA_PyQ2Rsov1RaNhadOEWGDcMbQcVFrWIoy3I,1580
matplotlib/tests/test_image.py,sha256=kU0we_NRlVWgZ6OjvrHbHbVsMcMZ1nTfbmtYqK3gorY,53038
matplotlib/tests/test_legend.py,sha256=IodJOw0yGKQlHXxIpKaxwAEylLoqO9QiFy-4jHmI4UE,54842
matplotlib/tests/test_lines.py,sha256=hqioyULLcVypE4oyCZoMzfMMXQbYRgKPYMmoKbQnRac,14791
matplotlib/tests/test_marker.py,sha256=w0WVHoaD-6iybjUSENoVFFdUOOR13943JcE4sgz3qhI,11410
matplotlib/tests/test_mathtext.py,sha256=SMzfo3HbfslCw7EmVYaWZgw975K0hMtjw6n0BQqn6pk,24513
matplotlib/tests/test_matplotlib.py,sha256=YPBZQdO6BEC4mPKxXD_W-HoFzoOZwGGifYrYdCDcy48,2887
matplotlib/tests/test_mlab.py,sha256=d4qMyogTFMrvlRZEpDs7SjhSmmCnBUMNgSX2bJU6eDk,42269
matplotlib/tests/test_offsetbox.py,sha256=_QeANIzM3oAlyx8RB8KO6-y1v7nBsIHV0DDOrLjymMI,16119
matplotlib/tests/test_patches.py,sha256=Da97JR1rG_Y__5iSMurf5S4XJlBJumR6CuWrKPDSjfU,31971
matplotlib/tests/test_path.py,sha256=xJgfg1wQ0AZdTX3fFEh4Qy37Wozx7yY0pzfOqSml2V0,19247
matplotlib/tests/test_patheffects.py,sha256=IqFuGi0wBB5ivyKAr2d4w_St48sbyuLVOSBmDK8FZWM,8089
matplotlib/tests/test_pickle.py,sha256=etOZ-QcYF15jmUJpzRzbhBZ90qL9Mb3VOYCAnQ3RyWs,9607
matplotlib/tests/test_png.py,sha256=xyXs9STg5WyoKCeqyK7zNoj5OGYlkJZIwP_GMIJqLTA,1240
matplotlib/tests/test_polar.py,sha256=NPrQ1BCLfcaJJYRc6dK84kfTxyBsHaTjet9_JenewVs,15578
matplotlib/tests/test_preprocess_data.py,sha256=cIVICUi1iahMQS30sqI5IlT2RYJRH2gJ0z60FyuYUFk,11363
matplotlib/tests/test_pyplot.py,sha256=b3ryM3bPeyZ3KYivwexyTbJbt6u27cTFKKKu6q17CCU,13077
matplotlib/tests/test_quiver.py,sha256=ABpTj72peaKpeSs-UgT1NTD3njMtVrvJ5aNDozpH_P4,10105
matplotlib/tests/test_rcparams.py,sha256=hdEPjrlEvNnpnFsoLMSm6K9jA-vgsDRpcRiPd5bcIa0,25426
matplotlib/tests/test_sankey.py,sha256=yg4i-qHT5MljFzFGViOnHbMkumM8bhPwRgoQ5M6CUEs,3900
matplotlib/tests/test_scale.py,sha256=3jUrfVsyxRE9FpmtWDC-ySo04E26s-ahDL9O3iKeEpQ,8411
matplotlib/tests/test_simplification.py,sha256=rq5RGw8dSwhCXEr-kUnCqLot_UUGH2JD30xZiIc0RN4,18869
matplotlib/tests/test_skew.py,sha256=vraGslq4buDkaugNIhK96K6BCswlJ-9ar-vpxFLA-eY,6340
matplotlib/tests/test_sphinxext.py,sha256=N-mey5MZWVaLxbN1wPvwqisV-u2ewvp9mA0uFQaPSEo,9724
matplotlib/tests/test_spines.py,sha256=q61XCKIySvm32S3imHA3SR5vVRGFVo-VUCx_LpKN_Fc,4893
matplotlib/tests/test_streamplot.py,sha256=FeZdKlfXvywlaM_YJp57IPRY-TNji5rklU4UgU1bmro,5723
matplotlib/tests/test_style.py,sha256=sd6rMLpPBS1hinR2svI1rXZmnXUnMQk1szxSNBUXtzU,6509
matplotlib/tests/test_subplots.py,sha256=OMgOqLT00zm6KD62jx5iiUJasCRVBU1K8G-6jmjBFiI,10766
matplotlib/tests/test_table.py,sha256=4H3-8JDKhcev4_lLSLm_HoJdYZuE29T2wm83iicbmos,8103
matplotlib/tests/test_testing.py,sha256=eh-1r4PIXcM7hfSKCNTU899QxRYWhZBg6W7x0TDVUxo,1057
matplotlib/tests/test_texmanager.py,sha256=j_5ZG02ztIO-m0oXzd31a13YFw_G7JR7Ldt3fwf-phI,2647
matplotlib/tests/test_text.py,sha256=7kfcbyqL4uAxHGQl7WgV4RyPTnjAd8Y4zCS2M6tSZVM,38473
matplotlib/tests/test_textpath.py,sha256=WLXvT5OzE6Tew4pr87LH-cgioCzM7srgMNRemiMEC5o,271
matplotlib/tests/test_ticker.py,sha256=ncU6yN3mcFpu2HjjjlPZHu230NNfEVyCClxbCx2wNuU,71991
matplotlib/tests/test_tightlayout.py,sha256=Hzv2oivfF_ajK2VS1yCZqUV0GsOcyYWSPcb5WsClHqo,12731
matplotlib/tests/test_transforms.py,sha256=MTsHEjZEWW63BnFgoAIgjElBdQ42x5DaVgpkGoL8U_g,46700
matplotlib/tests/test_triangulation.py,sha256=SlOePrPHzlwKiK9GIOXFkYEvsoO6oOSv3Cwq8ZxjMeI,54943
matplotlib/tests/test_ttconv.py,sha256=yW3YkGWTh-h9KXcyy70dEH4ytVuhMlzl-HMsNbK_gX8,540
matplotlib/tests/test_type1font.py,sha256=7TUXy8ehA3el-zvd4U9bnocYbelssDhUeph5cOFoaE4,6369
matplotlib/tests/test_units.py,sha256=B_4KZi4QE3M-5BXIxvz2Q2iU20II36LJp-XSeyfefyc,10039
matplotlib/tests/test_usetex.py,sha256=a-Y6NuyROPHDGP2ELsOZNSVwBUoGtAv1xQZfisl9lSE,6405
matplotlib/tests/test_widgets.py,sha256=RB8RBow51kNGGk1adppF6yQ-bmxSm0akNBTKBrPBL0U,67025
matplotlib/texmanager.py,sha256=zbnzSYgmoyTB3rapePXvbH9CrLMmxIL3GEPnV2KN1Ys,15098
matplotlib/texmanager.pyi,sha256=di3gbC9muXKTo5VCrW5ye-e19A1mrOl8e8lvI3b904A,1116
matplotlib/text.py,sha256=POLIW_kNgJJeuhbz4IgIXTW0MkQLVHgANHXSjVw1dbc,70583
matplotlib/text.pyi,sha256=LGMi0PLN7hlWeNVyS57bePXGjpXWhIy1AqyS2h7mCak,8027
matplotlib/textpath.py,sha256=TUyF7xSUzj-6_agCuGl-8kX57hBqsGZIROMHwWAZ1ro,13254
matplotlib/textpath.pyi,sha256=rqOeTAeQYgm2b2NpetrEX0gMF8PzzW43xS5mNfUA98M,2529
matplotlib/ticker.py,sha256=nYEBZfZpfR45leflKlkmy2Bmm-CWKjb5K4sIrB2qZ54,104617
matplotlib/ticker.pyi,sha256=konxZrZgNRz8PpaEHadIVa0WSIdRh5ORisDEuaUzSbA,10208
matplotlib/transforms.py,sha256=rfr3PQ8jEyscsXPQrGCbGE3gbTvzNi31Xc9-x0ACVKM,100003
matplotlib/transforms.pyi,sha256=E_I9VfgTtA3Df2mUROfwG-gyvccJ4KOCp3nFt1qWixc,11978
matplotlib/tri/__init__.py,sha256=asnfefKRpJv7sGbfddCMybnJInVDPwgph7g0mpoh2u4,820
matplotlib/tri/__pycache__/__init__.cpython-39.pyc,,
matplotlib/tri/__pycache__/_triangulation.cpython-39.pyc,,
matplotlib/tri/__pycache__/_tricontour.cpython-39.pyc,,
matplotlib/tri/__pycache__/_trifinder.cpython-39.pyc,,
matplotlib/tri/__pycache__/_triinterpolate.cpython-39.pyc,,
matplotlib/tri/__pycache__/_tripcolor.cpython-39.pyc,,
matplotlib/tri/__pycache__/_triplot.cpython-39.pyc,,
matplotlib/tri/__pycache__/_trirefine.cpython-39.pyc,,
matplotlib/tri/__pycache__/_tritools.cpython-39.pyc,,
matplotlib/tri/_triangulation.py,sha256=Ur2lKMOx4NrZxwyi0hBeBnVzicuKaCke0NkrZneSklM,9784
matplotlib/tri/_triangulation.pyi,sha256=pVw1rvpIcl00p7V7E9GcvJSqQWyoxlZXX_p0_VSxTiY,1017
matplotlib/tri/_tricontour.py,sha256=HFekvmC3eIUG1kw3O6NnyBHSRYjyF8pWqrIXbK53RoA,10239
matplotlib/tri/_tricontour.pyi,sha256=jnsAmVRX0-FOUw9ptUgci9J4T4JQRloKeH8fh8aAi-o,1155
matplotlib/tri/_trifinder.py,sha256=3gUzJZDIwfdsSJUE8hIKso9e1-UGvynUN9HxaqC1EEc,3522
matplotlib/tri/_trifinder.pyi,sha256=dXcZucacAS3Ch6nrDBPh2e3LYZLfZ7VwqpBUBb-vMPo,405
matplotlib/tri/_triinterpolate.py,sha256=4FtyJSoJpHcFxkSkZHZ1aNengVNWqVKF4l78PgCH8O0,62445
matplotlib/tri/_triinterpolate.pyi,sha256=SPuetoGqDlE5jo48yQQazqTY4NfcQ3_2ZYqEE6LFkTw,964
matplotlib/tri/_tripcolor.py,sha256=3j5J67vO3HuTAfnaZm93wyilpmEX-bX4eiJNAsbOZJM,6275
matplotlib/tri/_tripcolor.pyi,sha256=QsA-A2ohj3r_tAElt2-9pzi47JiU01tNlRPDIptqnh4,1781
matplotlib/tri/_triplot.py,sha256=jlHSz36Z5S18zBKc639PlSqdhfl7jHol8ExlddJuDI4,3102
matplotlib/tri/_triplot.pyi,sha256=9USU-BfitrcdQE8yWOUlBX59QBNoHCWivDon9JbDQ0k,446
matplotlib/tri/_trirefine.py,sha256=NG8bsDhZ5EOxMT-MsEWzJm11ZR3_8CAYHlG53IGi0ps,13178
matplotlib/tri/_trirefine.pyi,sha256=J_PmjbeI6UbLaeecgj1OCvGe_sr9UUsNK9NGBSlQ320,1056
matplotlib/tri/_tritools.py,sha256=wC9KVE6UqkWVHpyW9FU4hQdqRVRVmJlhaBF1EXsaD8U,10575
matplotlib/tri/_tritools.pyi,sha256=XWwwvH2nIAmH8k59aRjnLBVQbTwKvd_FzdsRNASCJMw,402
matplotlib/typing.py,sha256=CaCkp84zE_7aDG_Ee6mZfI5k4LG60f354orvBrIvLLg,2098
matplotlib/units.py,sha256=7O-llc8k3GpdotUs2tWcEGgoUHHX-Y7o0R7f-1Jve3k,6429
matplotlib/widgets.py,sha256=nzcLR5-PaH2KUfLW9V_G38OeEc5wx1VX5m4afv4HMCQ,151735
matplotlib/widgets.pyi,sha256=rZjyEMCNKR8Xz-igAZj7rxh4ZoOqUzis3SuEIV6qSKs,15375
mpl_toolkits/axes_grid1/__init__.py,sha256=wiuUCQo1g20SW5T3mFOmI9dGCJY6aDmglpQw5DfszEU,371
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-39.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-39.pyc,,
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=pRyYM69J3iEdIZq5IpbCOQBXD6LbkiE-SPCEh-ZDKw4,19211
mpl_toolkits/axes_grid1/axes_divider.py,sha256=Q5mdTLc7k5z660u53c-XdfNf_cDSFIGnqp9Tgb3uXJQ,24557
mpl_toolkits/axes_grid1/axes_grid.py,sha256=4ZOhjLn_29I3Q0QZZbnAjEN7e9MwmW-aydrQC4LqKko,22297
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=pabgaWJuLTCPw2FlT6Zfy5d0_95CEvaLeosWRTElR98,5227
mpl_toolkits/axes_grid1/axes_size.py,sha256=ize47FCDCehkvBu1wxoaRt3BRUjubjaQdBjdm92oDZU,7029
mpl_toolkits/axes_grid1/inset_locator.py,sha256=0FzpYiTGKKHewYFzDGP_giwVm5dMGtdbbDbyjXKqD28,21133
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=vFCttnj9JIgY3Mt2eOi-O_FVvdZ6SW_sBtIBFib6bz4,4251
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=T-ve7kZbh3WLKPSr6X7MrFfwN9LaQh71i1ADtJ-Feyk,9416
mpl_toolkits/axes_grid1/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/axes_grid1/tests/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/conftest.cpython-39.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/test_axes_grid1.cpython-39.pyc,,
mpl_toolkits/axes_grid1/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axes_grid1/tests/test_axes_grid1.py,sha256=VxF-VNnYvad5sqMGMqP0JWbNADCpBtyG8_EO94l5eVc,29401
mpl_toolkits/axisartist/__init__.py,sha256=RPaNDl22FbmDP7ZRsku1yCqpoNqcclCk0a3rXj3G7fE,631
mpl_toolkits/axisartist/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_grid.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_rgb.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-39.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-39.pyc,,
mpl_toolkits/axisartist/angle_helper.py,sha256=-mjKpaR1pLMJuoc0sx0_V3bv0iRPMrpS7r_WI0UYrCc,12952
mpl_toolkits/axisartist/axes_divider.py,sha256=ltdw9uabafN8MXqTcmtpA2mVFKmDqswCDzPZA6Qp2oo,135
mpl_toolkits/axisartist/axes_grid.py,sha256=MiF5JTwOy8mKBpicIQk_itM0Mvcaq9-ctdayPR3JQto,593
mpl_toolkits/axisartist/axes_rgb.py,sha256=gfqcc7N3iAFGfShu1g2516unMj5hvNoeF2YRSJslopQ,532
mpl_toolkits/axisartist/axis_artist.py,sha256=lzFeyR1RCIvRFv2SCz-FNQHugQjtEMU2rX4fWn7XXnI,38158
mpl_toolkits/axisartist/axisline_style.py,sha256=9jbDkXEzMQiDHR-lDYKZEvTADtJwt2qlN1cErVUUdx0,6723
mpl_toolkits/axisartist/axislines.py,sha256=yzP-0k5dkC4jiUmxpADds1Ygl3bPbkCz4c7US_5hs4g,16708
mpl_toolkits/axisartist/floating_axes.py,sha256=-Zmt33-KlX8h5BXuf_DNE518JV7tOM_A_X7JCae-VkY,10670
mpl_toolkits/axisartist/grid_finder.py,sha256=Hi2zwnQilavgrqWKScuSRba_WBPqqrbmErGU0XyAsOo,12265
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=ofN7pPqEMh3r6bSf9eOHkAZEOrmhiNZV-Yig3jozkC4,12349
mpl_toolkits/axisartist/parasite_axes.py,sha256=Ydi4-0Lbczr6K7Sz1-fRwK4Tm8KlHrOIumx67Xbo_9c,244
mpl_toolkits/axisartist/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/axisartist/tests/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/conftest.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_angle_helper.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axis_artist.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axislines.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_floating_axes.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_finder.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_helper_curvelinear.cpython-39.pyc,,
mpl_toolkits/axisartist/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axisartist/tests/test_angle_helper.py,sha256=PwhJwBm2kk4uMyhdO5arQs8IlqSX2vN0hvUzI7YHqrw,5670
mpl_toolkits/axisartist/tests/test_axis_artist.py,sha256=wt3bicVgUPnBX48-dH0Z6hboHgutIgwVpaGkcUZDeVU,2980
mpl_toolkits/axisartist/tests/test_axislines.py,sha256=fmAVzqSgbLdqtBhq1G0DADySb6Rx5wdgMJUPlfEB-pk,4361
mpl_toolkits/axisartist/tests/test_floating_axes.py,sha256=l24VB1SLrsJZOMMH2jmBny9ETha4AqAM5KokdGOa5Wk,4083
mpl_toolkits/axisartist/tests/test_grid_finder.py,sha256=cwQLDOdcJbAY2E7dr8595yzuNh1_Yh80r_O8WGT2hMY,1156
mpl_toolkits/axisartist/tests/test_grid_helper_curvelinear.py,sha256=OhHej0vCfCjJJknT7yIt4OxZd6OMJCXnFoT3pzqUtTo,7216
mpl_toolkits/mplot3d/__init__.py,sha256=fH9HdMfFMvjbIWqy2gjQnm2m3ae1CvLiuH6LwKHo0kI,49
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/art3d.py,sha256=B2obITqkuza4uXjPo_YylYTLLl60ZQD-G3FFvCdpYak,43048
mpl_toolkits/mplot3d/axes3d.py,sha256=_Q_Xj1jqBsjYxt4BqmUfm8QG_QWykvItQdO669-nDIk,141531
mpl_toolkits/mplot3d/axis3d.py,sha256=VTA4TmBVlsXVqNKYlKGnqsYRRpdaY026eUCdarGKPCk,29809
mpl_toolkits/mplot3d/proj3d.py,sha256=exvdG39Py5cXCzzfGnLJSvn5MZ-EE-MRRGejMdAW0Aw,6933
mpl_toolkits/mplot3d/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/mplot3d/tests/__pycache__/__init__.cpython-39.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/conftest.cpython-39.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_art3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_axes3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_legend3d.cpython-39.pyc,,
mpl_toolkits/mplot3d/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/mplot3d/tests/test_art3d.py,sha256=1XCIo8xuKYxRDs34oxcjT8t2O1zCSFkK3u8Z753s3sk,2182
mpl_toolkits/mplot3d/tests/test_axes3d.py,sha256=YWtJEdgyP5Cp0-gFatjl8v9ktUosGnJR8EDuC5dnMYA,83231
mpl_toolkits/mplot3d/tests/test_legend3d.py,sha256=A6CWWz3q5VqYu7WRhwALSWMYkPwj941sQ5GErWLdBZ8,4342
pylab.py,sha256=zUXU0l7e7C5jmDSJbM0GLQxBun3xzuXNf1tuoZYA6Xk,110
