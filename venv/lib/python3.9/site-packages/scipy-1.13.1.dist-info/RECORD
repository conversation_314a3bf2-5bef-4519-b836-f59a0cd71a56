scipy-1.13.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.13.1.dist-info/LICENSE.txt,sha256=OwXcBprxej4RIjXP-QcgMnIje8NFcizR5QwZIGKuBQo,46808
scipy-1.13.1.dist-info/METADATA,sha256=HctRIR2YC6da0zrrRSFt0njnxCSrFx10i82ubWtYBgM,60571
scipy-1.13.1.dist-info/RECORD,,
scipy-1.13.1.dist-info/WHEEL,sha256=QxgXZuTayQ0s2zrj9jZLeeDHMuZQgOoZGj8XYuSuLx0,92
scipy/.dylibs/libgcc_s.1.1.dylib,sha256=_IQbXR6GhUP0Ya8X3XNaeutzYXrVb1xj6yvIdLZ0Ri0,126416
scipy/.dylibs/libgfortran.5.dylib,sha256=lfHrlYaHF1QaY1i1fDqeXXW06FREHkfy1VsgWyWQsWc,6786304
scipy/.dylibs/libopenblas.0.dylib,sha256=CJwfPhRBycu80i5IKaFLMd7GXSfGgEpPlPyWnk_-NDc,68969792
scipy/.dylibs/libquadmath.0.dylib,sha256=iswtS7EKolY56cUsCgxPWJAN7-YXsNsGCf-f1aHLKv4,352704
scipy/__config__.py,sha256=orou71CIdj4c6UiY9RIyW0301Svib8CZO7IJI99NiTU,5238
scipy/__init__.py,sha256=8J2KNCrLUruYIHP76yWU2TY_9VQz091xAGYULbfAvuk,4144
scipy/__pycache__/__config__.cpython-39.pyc,,
scipy/__pycache__/__init__.cpython-39.pyc,,
scipy/__pycache__/_distributor_init.cpython-39.pyc,,
scipy/__pycache__/conftest.cpython-39.pyc,,
scipy/__pycache__/version.cpython-39.pyc,,
scipy/_distributor_init.py,sha256=zJThN3Fvof09h24804pNDPd2iN-lCHV3yPlZylSefgQ,611
scipy/_lib/__init__.py,sha256=CXrH_YBpZ-HImHHrqXIhQt_vevp4P5NXClp7hnFMVLM,353
scipy/_lib/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/__pycache__/_array_api.cpython-39.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-39.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-39.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-39.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-39.pyc,,
scipy/_lib/__pycache__/_elementwise_iterative_method.cpython-39.pyc,,
scipy/_lib/__pycache__/_finite_differences.cpython-39.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-39.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-39.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-39.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-39.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-39.pyc,,
scipy/_lib/__pycache__/_util.cpython-39.pyc,,
scipy/_lib/__pycache__/decorator.cpython-39.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-39.pyc,,
scipy/_lib/__pycache__/doccer.cpython-39.pyc,,
scipy/_lib/__pycache__/uarray.cpython-39.pyc,,
scipy/_lib/_array_api.py,sha256=Ibx-wfA11m7xKtNIlvYhS4e71GyehsGnUVxlcLKF4Rs,12740
scipy/_lib/_bunch.py,sha256=WooFxHL6t0SwjcwMDECM5wcWWLIS0St8zP3urDVK-V0,8120
scipy/_lib/_ccallback.py,sha256=N9CO7kJYzk6IWQR5LHf_YA1-Oq48R38UIhJFIlJ2Qyc,7087
scipy/_lib/_ccallback_c.cpython-39-darwin.so,sha256=XMf2etaeuNWPA_7yR2BUDLDbySvYQU127YJkl59kNyY,120136
scipy/_lib/_disjoint_set.py,sha256=o_EUHZwnnI1m8nitEf8bSkF7TWZ65RSiklBN4daFruA,6160
scipy/_lib/_docscrape.py,sha256=B4AzU5hrwyo8bJLBlNU-PQ0qCtgStZe_LasHc2Q9ZwE,21498
scipy/_lib/_elementwise_iterative_method.py,sha256=w3qm_WWCu4nrtcbdnX8Wx2SKRYpamMfeyxjfmyvBONs,13509
scipy/_lib/_finite_differences.py,sha256=llaIPvCOxpE4VA8O8EycPEU8i6LHJyOD-y7Y9OvQHt0,4172
scipy/_lib/_fpumode.cpython-39-darwin.so,sha256=TFG0Ji6KA2R7aYAOeV3nxP7ZZUj7DTbOsR-1Ln1YDGw,33256
scipy/_lib/_gcutils.py,sha256=hajQd-HUw9ckK7QeBaqXVRpmnxPgyXO3QqqniEh7tRk,2669
scipy/_lib/_pep440.py,sha256=vo3nxbfjtMfGq1ektYzHIzRbj8W-NHOMp5WBRjPlDTg,14005
scipy/_lib/_test_ccallback.cpython-39-darwin.so,sha256=CJ1RoLQB8KNOQHVGHT2xp2xCltMeu323jCIp7NSSF5o,36176
scipy/_lib/_test_deprecation_call.cpython-39-darwin.so,sha256=YhI3tbfAZ3HWAojGEVFFaXr5vcEnpPMP3ydJRLp28HE,57800
scipy/_lib/_test_deprecation_def.cpython-39-darwin.so,sha256=sfCft0GN5kMs4A0RKzcCSEZxx-lDydPgDCaULrytD1o,39568
scipy/_lib/_testutils.py,sha256=JtE6ksxrUr0E-A8sEXazvoXvnHympmXabXCys0dRtjU,8134
scipy/_lib/_threadsafety.py,sha256=xuVqUS2jv46fOOQf7bcrhiYtnPVygqmrIVJc-7_LlI8,1455
scipy/_lib/_tmpdirs.py,sha256=z3IYpzACnWdN_BMjOvqYbkTvYyUbfbQvfehq7idENSo,2374
scipy/_lib/_uarray/LICENSE,sha256=yAw5tfzga6SJfhTgsKiLVEWDNNlR6xNhQC_60s-4Y7Q,1514
scipy/_lib/_uarray/__init__.py,sha256=Rww7wLA7FH6Yong7oMgl_sHPpjcRslRaTjh61W_xVg4,4493
scipy/_lib/_uarray/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-39.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=CeTV7H8oXRs7wrdBu9MXqz5-5EtRyzXnDrTlsMWtyt8,20432
scipy/_lib/_uarray/_uarray.cpython-39-darwin.so,sha256=fAYaL7HwWnUW6uFxPf2wQjPlucKPOIT0fa6-LU2mWng,103552
scipy/_lib/_util.py,sha256=zPHnzzCxXrbHdiejH81_MRL6K0P84SG1S-Bq6sDN6j8,32217
scipy/_lib/array_api_compat/__init__.py,sha256=sC0Ht3rsA1SxX6cuBmBSe2mJ8_m2SODKN29BjIxlwP8,946
scipy/_lib/array_api_compat/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/array_api_compat/__pycache__/_internal.cpython-39.pyc,,
scipy/_lib/array_api_compat/_internal.py,sha256=RiQvh6ZoZLXw0l2CYKMG_6_PwmDO3qm7Hay8MMpgObc,987
scipy/_lib/array_api_compat/common/__init__.py,sha256=fH4Ux-dWyQRkZ6WxqDTv-Bges_uKQ80TgTKOxvZ2MFE,24
scipy/_lib/array_api_compat/common/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_aliases.cpython-39.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_helpers.cpython-39.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_linalg.cpython-39.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_typing.cpython-39.pyc,,
scipy/_lib/array_api_compat/common/_aliases.py,sha256=P6-5PJI0ZzVPS58CwpAVh__B8TkVMK7_4DYy8SbpC3A,16263
scipy/_lib/array_api_compat/common/_helpers.py,sha256=Rn-aG4Vu56auzREAnmkhEsQMr9z__4sgEUEQq2E0elA,8206
scipy/_lib/array_api_compat/common/_linalg.py,sha256=4D1-ukLTf7s3t6LaFsoR_mMkblceSywx4cYXbeeqZ28,6301
scipy/_lib/array_api_compat/common/_typing.py,sha256=Wfsx0DJSMTIGfMoj_tqH2-HjxPyVSbQ9aUB02FaEYsA,388
scipy/_lib/array_api_compat/cupy/__init__.py,sha256=g9IFwPzeOhMXnR-c-Qf8QFXfAltPp6SlS9AtZrjKAQw,397
scipy/_lib/array_api_compat/cupy/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_aliases.cpython-39.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_typing.cpython-39.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/linalg.cpython-39.pyc,,
scipy/_lib/array_api_compat/cupy/_aliases.py,sha256=bKFKl2rLDX9r74Arv-HZg2yj-ZZqRwGbNoUZnsSORgM,2602
scipy/_lib/array_api_compat/cupy/_typing.py,sha256=oDhrZB8R-D6wvee7tR4YkyBhTq93M0fFi3Tv-lpN_Dg,617
scipy/_lib/array_api_compat/cupy/linalg.py,sha256=KidQHA9W3gBTRtWZ9963XiMXel-TvFCSecqB3Te0G9o,1358
scipy/_lib/array_api_compat/numpy/__init__.py,sha256=bhqr1ecsSl-w5N_TnaaItHsT3eWnNtsC5H5C_6zFu7o,596
scipy/_lib/array_api_compat/numpy/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_aliases.cpython-39.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_typing.cpython-39.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/linalg.cpython-39.pyc,,
scipy/_lib/array_api_compat/numpy/_aliases.py,sha256=xmcLK4lvyXgrPQNnNuwXut0LYcKBzxruvcQxXcSEjOI,2606
scipy/_lib/array_api_compat/numpy/_typing.py,sha256=OFRXfhT8-snL_4VeOjbOCd_yYIGqVS-IRrZoWNcL3v4,618
scipy/_lib/array_api_compat/numpy/linalg.py,sha256=e3gqAyX01YCMHYrQ0rGZ8haub9ZhfHv8TZe1haaRkpE,1189
scipy/_lib/array_api_compat/torch/__init__.py,sha256=MWtkg6kdsN8CaTgYQJvjVMZu3RQq2mUkyme7yfkUWSE,518
scipy/_lib/array_api_compat/torch/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_aliases.cpython-39.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/linalg.cpython-39.pyc,,
scipy/_lib/array_api_compat/torch/_aliases.py,sha256=s-1HnikHDhbBGBDquuiulALiQohOthMOPbonWuV4Fuk,26792
scipy/_lib/array_api_compat/torch/linalg.py,sha256=H6lb-umJYLcrGCEaaaH___3rJkk6dnfXNntU8tyt20E,2485
scipy/_lib/decorator.py,sha256=ILVZlN5tlQGnmbgzNKH2TTcNzGKPlHwMuYZ8SbSEORA,15040
scipy/_lib/deprecation.py,sha256=nAiyFAWEH2Bk5P5Hy_3HSUM3v792GS9muBKr-fdj3Yk,8074
scipy/_lib/doccer.py,sha256=shdWIi3u7QBN5CyyKwqWW99qOEsiFewB8eH10FWhYLM,8362
scipy/_lib/messagestream.cpython-39-darwin.so,sha256=xn9MDB3CGPOgtB5M-vVgxD6fy9DFxXU8XnV57yTi_os,81424
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_array_api.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-39.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-39.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=qvfxvemSmGvaqcpHwoEzdXYn5mrAf-B1X5qGGyasPC4,3416
scipy/_lib/tests/test__pep440.py,sha256=u9hPoolK4AoIIS-Rq74Du5SJu5og2RxMwgaAvGgWvRo,2277
scipy/_lib/tests/test__testutils.py,sha256=P4WDJpUgy19wD9tknQSjIivuQvZF7YUBGSBWlur2QRA,800
scipy/_lib/tests/test__threadsafety.py,sha256=qSfCF5OG_5lbnSl-grmDN_QCU4QLe-fS3sqnwL04pf8,1322
scipy/_lib/tests/test__util.py,sha256=lG711zcPwi8uNPrMkgwGHqIKbEPHhlU8lYj6gWVT9aA,14479
scipy/_lib/tests/test_array_api.py,sha256=6y0vlLDf5UaMglwzdN-gWqp14EgT5N2blDYjR_5OYyE,4039
scipy/_lib/tests/test_bunch.py,sha256=sViE5aFSmAccfk8kYvt6EmzR5hyQ9nOSWMcftaDYDBg,6168
scipy/_lib/tests/test_ccallback.py,sha256=dy9g70zyd80KpawffSKgWbddsKUwNNeF5sbxMfCTk6w,6175
scipy/_lib/tests/test_deprecation.py,sha256=a_3r_9pFx1sxJXeFgiTSV9DXYnktc4fio1hR0ITPywA,364
scipy/_lib/tests/test_import_cycles.py,sha256=lsGEBuEMo4sbYdZNSOsxAQIJgquUIjcDhQjtr0cyFg4,500
scipy/_lib/tests/test_public_api.py,sha256=vT2kkjgtkMhxPq3mAoQOZnoD5HEHabHMWrBVW4UsvvE,19234
scipy/_lib/tests/test_scipy_version.py,sha256=jgo-2YhCkBksXHM6xKiN_iJJZkqz0CvXqn2jVxx1djA,606
scipy/_lib/tests/test_tmpdirs.py,sha256=URQRnE_lTPw9MIJYBKXMfNATQ0mpsBDgoqAowkylbWQ,1240
scipy/_lib/tests/test_warnings.py,sha256=MnTTTqcMhloMzL0BeZ2JN2oAL0JKzjZ7UY3IOjOrMQs,4546
scipy/_lib/uarray.py,sha256=4X0D3FBQR6HOYcwMftjH-38Kt1nkrS-eD4c5lWL5DGo,815
scipy/cluster/__init__.py,sha256=LNM_kFbT28cIYYgctilxYsxdjuF3KuiOaulZH4dFatE,876
scipy/cluster/__pycache__/__init__.cpython-39.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-39.pyc,,
scipy/cluster/__pycache__/vq.cpython-39.pyc,,
scipy/cluster/_hierarchy.cpython-39-darwin.so,sha256=MjW3HFwtmVUrFHVD33u5egaxTMbGGaSA57bOl-EOyjw,376872
scipy/cluster/_optimal_leaf_ordering.cpython-39-darwin.so,sha256=2frjbU21YW7_wQxuP8hHHL77UDtE3OQgI-gdnHQ_A7c,265256
scipy/cluster/_vq.cpython-39-darwin.so,sha256=jJdjkT6Z3RDMPcMdjl03l7oH3Ea7DGxrtxhHNErhOX4,133400
scipy/cluster/hierarchy.py,sha256=XHNOlJBrIReWElJN1MfosbN12aE5jSxsZD-KtTKa-F0,148588
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-39.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-39.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=7syUYdIaDVr7hgvMliX0CW4386utjBJn1DOgX0USXls,6850
scipy/cluster/tests/test_disjoint_set.py,sha256=EuHGBE3ZVEMnWFbCn8tjI-_6CWrNXfpnv5bUBa9qhWI,5525
scipy/cluster/tests/test_hierarchy.py,sha256=qVwLvvVO7iJNfqWJWdXia1oXOY-T6s09Yf58IuNG6zc,48726
scipy/cluster/tests/test_vq.py,sha256=pSUokcwvp50iWwyrlNN53VxCaShDCScjRMJ6hcISyWc,17609
scipy/cluster/vq.py,sha256=abgPHLJDSEH8mwGaGMtMG1rmkI09P272ji0yfMcjmN4,30738
scipy/conftest.py,sha256=7ocP1roANCCWR6A8lCUUGFoWHX-HAPEo2bUdvbvx-Ag,9034
scipy/constants/__init__.py,sha256=Pvyiayo6WX0cVORlr-Ap0VacI5hu5C8PQ17HIwgLcTc,12437
scipy/constants/__pycache__/__init__.cpython-39.pyc,,
scipy/constants/__pycache__/_codata.cpython-39.pyc,,
scipy/constants/__pycache__/_constants.cpython-39.pyc,,
scipy/constants/__pycache__/codata.cpython-39.pyc,,
scipy/constants/__pycache__/constants.cpython-39.pyc,,
scipy/constants/_codata.py,sha256=AAXUgkUuVsGHJ0axSfGyxTd8MkPV6yiza-Q2MSJyt58,155635
scipy/constants/_constants.py,sha256=CcZ7BBKx8NuVpvjBeS0lY0I1yg5lnhSVhLPKGjIMaPU,10376
scipy/constants/codata.py,sha256=RMD4V770zdsftqP4MN559SKUq1J15dwWStdID0Z_URE,794
scipy/constants/constants.py,sha256=w7sGxSidD2Q9Ged0Sn1pnL-qqD1ssEP1A8sZWeLWBeI,2250
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-39.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-39.pyc,,
scipy/constants/tests/test_codata.py,sha256=ToO_lhQOsusJlP3QjrYqa1vw7x6wTCuKH17fg87tH08,1959
scipy/constants/tests/test_constants.py,sha256=PY1oy6bbM2zoPAPgUeBqVThnVRuu4lBt_uMmxm7Ct38,1632
scipy/datasets/__init__.py,sha256=7IzOi9gij2mhYCCMWJE1RiI22E1cVbe6exL9BRm1GXs,2802
scipy/datasets/__pycache__/__init__.cpython-39.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-39.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-39.pyc,,
scipy/datasets/__pycache__/_registry.cpython-39.pyc,,
scipy/datasets/__pycache__/_utils.cpython-39.pyc,,
scipy/datasets/_download_all.py,sha256=iRPR2IUk6C3B5u2q77yOhac449MRSoRaTlCy2oCIknE,1701
scipy/datasets/_fetchers.py,sha256=Jt8oklMEdZSKf0yJddYCarjlMcOl1XRsdv1LW8gfwE0,6760
scipy/datasets/_registry.py,sha256=br0KfyalEbh5yrQLznQ_QvBtmN4rMsm0UxOjnsJp4OQ,1072
scipy/datasets/_utils.py,sha256=kdZ-Opp7Dr1pCwM285p3GVjgZTx_mKWCvETur92FWg4,2967
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-39.pyc,,
scipy/datasets/tests/test_data.py,sha256=GelFTF2yZqiiQkgTv8ukv8sKTJBdmpsyK5fr0G6z7Ls,4064
scipy/fft/__init__.py,sha256=XjfuqqFtHktAmDhKoFSca5JoYqCaQxtZRdH0SlPNYjM,3513
scipy/fft/__pycache__/__init__.cpython-39.pyc,,
scipy/fft/__pycache__/_backend.cpython-39.pyc,,
scipy/fft/__pycache__/_basic.cpython-39.pyc,,
scipy/fft/__pycache__/_basic_backend.cpython-39.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-39.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-39.pyc,,
scipy/fft/__pycache__/_fftlog_backend.cpython-39.pyc,,
scipy/fft/__pycache__/_helper.cpython-39.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-39.pyc,,
scipy/fft/__pycache__/_realtransforms_backend.cpython-39.pyc,,
scipy/fft/_backend.py,sha256=5rBxK8GQtCMnuPHc-lNQdpH4uFFZ9_5vBukkDv6jRRA,6544
scipy/fft/_basic.py,sha256=lGJ8qQTMXUJEbq_2vwfPPPlX7b4j358ks9LLretOtEY,62997
scipy/fft/_basic_backend.py,sha256=BnexiVV20wvTXBPYbY89v_mCL6hzP7iF6w_ahG7EgHQ,6546
scipy/fft/_debug_backends.py,sha256=RlvyunZNqaDDsI3-I6QH6GSBz_faT6EN4OONWsvMtR8,598
scipy/fft/_fftlog.py,sha256=_ryVlUuSQp_J0hH8VFGMRn4ZvzudHqKDYCVbpV-WVsY,7866
scipy/fft/_fftlog_backend.py,sha256=K-nbAr00YkJ0G5Y_WSe5aorImbnVswKQcRkGSaYLs38,5237
scipy/fft/_helper.py,sha256=U47qLBvBl6cs6eicfdq1nldfUVs70Nw0ByOCZmuqAG0,10048
scipy/fft/_pocketfft/LICENSE.md,sha256=wlSytf0wrjyJ02ugYXMFY7l2D8oE8bdGobLDFX2ix4k,1498
scipy/fft/_pocketfft/__init__.py,sha256=dROVDi9kRvkbSdynd3L09tp9_exzQ4QqG3xnNx78JeU,207
scipy/fft/_pocketfft/__pycache__/__init__.cpython-39.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-39.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-39.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-39.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=4HR-eRDb6j4YR4sqKnTikFmG0tnUIXxa0uImnB6_JVs,8138
scipy/fft/_pocketfft/helper.py,sha256=lVpf-oCVBU-TAcreDe15vfbZwpxbfvCGzut0w9cu-As,5807
scipy/fft/_pocketfft/pypocketfft.cpython-39-darwin.so,sha256=1igGCp-8W4Kj2lfaXkVnbsCiejYJpn7FDi9_zSWSAPQ,1122912
scipy/fft/_pocketfft/realtransforms.py,sha256=4TmqAkCDQK3gs1ddxXY4rOrVfvQqO8NyVtOzziUGw6E,3344
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-39.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=TviTxRl-MOQPcBgu-vvGU_wOunD59HQCc8k2-IdV3X4,35373
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=wn3Lgln-PL2OpSoWjKa4G4mXmngT-mLkOuZTZl3jxK0,16656
scipy/fft/_realtransforms.py,sha256=QmO9CDqrAsvBcLNgIzFBIWBTYsSUCRJ_Cj1myv73KlE,25386
scipy/fft/_realtransforms_backend.py,sha256=u4y4nBGCxpTLVqxK1J7xV6tcpeC3-8iiSEXLOcRM9wI,2389
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-39.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-39.pyc,,
scipy/fft/tests/mock_backend.py,sha256=RAlVSy4Qtk1oTaEG9fl4WKonoSijVHIDfxqv5MbVBPY,2554
scipy/fft/tests/test_backend.py,sha256=KnLuBO1gQcuaLlr2IP8ndhn2hNFe24EiKPvqbv4o1I4,4275
scipy/fft/tests/test_basic.py,sha256=CRtrf1R8UoZiKrHKBgzyUK4jpAOkqmSXS55seksgHPI,21216
scipy/fft/tests/test_fftlog.py,sha256=iRvVB54ZMJSJG52bE-t3mqfHDHesuxnfD1phNAScyGo,6173
scipy/fft/tests/test_helper.py,sha256=8ynydSBXgDSA5uHjrSI891wYOpF7g4veIJ536Iv535Q,15436
scipy/fft/tests/test_multithreading.py,sha256=Ub0qD3_iSApPT9E71i0dvKnsKrctLiwMq95y3370POE,2132
scipy/fft/tests/test_real_transforms.py,sha256=sN5XJmLrnmlIBr7Z5GWYeOCZNQs3_8bAgVL44ShP0c8,8621
scipy/fftpack/__init__.py,sha256=rLCBFC5Dx5ij_wmL7ChiGmScYlgu0mhaWtrJaz_rBt0,3155
scipy/fftpack/__pycache__/__init__.cpython-39.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-39.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-39.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-39.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-39.pyc,,
scipy/fftpack/__pycache__/basic.cpython-39.pyc,,
scipy/fftpack/__pycache__/helper.cpython-39.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-39.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-39.pyc,,
scipy/fftpack/_basic.py,sha256=Sk_gfswmWKb3za6wrU_mIrRVBl69qjzAu9ltznbDCKs,13098
scipy/fftpack/_helper.py,sha256=g5DZnOVLyLw0BRm5w9viScU3GEPmHwRCwy5dcHdJKb4,3350
scipy/fftpack/_pseudo_diffs.py,sha256=eCln0ZImNYr-wUWpOZ-SmKKIbhJsV8VBLmwT_C79RsQ,14200
scipy/fftpack/_realtransforms.py,sha256=ledb21L13ofGnOU4pkx8uWuARCxsh3IFQrHctxTgzzw,19214
scipy/fftpack/basic.py,sha256=i2CMMS__L3UtFFqe57E0cs7AZ4U6VO-Ted1KhU7_wNc,577
scipy/fftpack/convolve.cpython-39-darwin.so,sha256=N6b0e239cjGIqzQhEPB4eZxF6NQkb9TP32UntsQXXQU,227416
scipy/fftpack/helper.py,sha256=M7jTN4gQIRWpkArQR13bI7WN6WcW-AabxKgrOHRvfeQ,580
scipy/fftpack/pseudo_diffs.py,sha256=RqTDJRobZQGZg6vSNf4FBzFdLTttkqdWTGchttuQhDo,674
scipy/fftpack/realtransforms.py,sha256=9-mR-VV3W14oTaD6pB5-RIDV3vkTBQmGCcxfbA8GYH0,595
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-39.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-39.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=nLMulUtVIcsVzahpYuSvuEqGHgLeCwpar5YhLbtiTxI,30307
scipy/fftpack/tests/test_helper.py,sha256=8JaPSJOwsk5XXOf1zFahJ_ktUTfNGSk2-k3R6e420XI,1675
scipy/fftpack/tests/test_import.py,sha256=Sz4ZZmQpz_BtiO0Gbtctt6WB398wB17oopv5mkfOh0U,1120
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=SEVPHPDdSxDSUCC8qkwuKD7mIX8rFIx9puxGzBYd1uk,13389
scipy/fftpack/tests/test_real_transforms.py,sha256=W-gHxBHV3elIPFDOuZvSfZkEuMYJ6edjG7fL-3vVY1s,23971
scipy/integrate/__init__.py,sha256=Nb06g1FvgETDPfultR4y_JGZCR31k9xrvpcq5VtoGPo,4236
scipy/integrate/__pycache__/__init__.cpython-39.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-39.pyc,,
scipy/integrate/__pycache__/_ode.cpython-39.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-39.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-39.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-39.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-39.pyc,,
scipy/integrate/__pycache__/_tanhsinh.cpython-39.pyc,,
scipy/integrate/__pycache__/dop.cpython-39.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-39.pyc,,
scipy/integrate/__pycache__/odepack.cpython-39.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-39.pyc,,
scipy/integrate/__pycache__/vode.cpython-39.pyc,,
scipy/integrate/_bvp.py,sha256=7OiL3Kg7IZlmUkcrBy6qzyjhayV546_HlB6kb6o7zh4,40927
scipy/integrate/_dop.cpython-39-darwin.so,sha256=0GTsQsceDlvMGZgqex3aJx4ycJUbcNe0S54qQLX_5Z0,127168
scipy/integrate/_ivp/__init__.py,sha256=gKFR_pPjr8fRLgAGY5sOzYKGUFu2nGX8x1RrXT-GZZc,256
scipy/integrate/_ivp/__pycache__/__init__.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-39.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-39.pyc,,
scipy/integrate/_ivp/base.py,sha256=Mlef_dgmn0wzjFxZA3oBbtHrQgrfdZw_8k1mLYNZP4A,10295
scipy/integrate/_ivp/bdf.py,sha256=deQVxWq58ihFDWKC8teztUbe8MYN4mNgLCU-6aq_z1U,17522
scipy/integrate/_ivp/common.py,sha256=A6_X4WD0PwK-6MhOAmU8aj8CLuVdlxfBlKdPNxab-lE,15274
scipy/integrate/_ivp/dop853_coefficients.py,sha256=OrYvW0Hu6X7sOh37FU58gNkgC77KVpYclewv_ARGMAE,7237
scipy/integrate/_ivp/ivp.py,sha256=C5jQvVgpf0cBo_khaVO_bE9Mh8V-yOadv_xzc8FXKsQ,31472
scipy/integrate/_ivp/lsoda.py,sha256=t5t2jZBgBPt0G20TOI4SVXuGFAZYAhfDlJZhfCzeeDo,9927
scipy/integrate/_ivp/radau.py,sha256=7Ng-wYOdOBf4ke4-CYyNUQUH3jgYmDflpE1UXIYNOdU,19743
scipy/integrate/_ivp/rk.py,sha256=kYWCzolgXwnDuDIqDViI2Exzu61JekmbbCYuQhGYsgA,22781
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-39.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-39.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=Y1pItTm6-38k1_nDMrWTKwa36vmxd2234gq4uDReUOs,37088
scipy/integrate/_ivp/tests/test_rk.py,sha256=K9UxZghBzSL2BzmgLndPJcWOWV4Nr530TGKWakpsoeM,1326
scipy/integrate/_lsoda.cpython-39-darwin.so,sha256=VoclPCPmp7EcLpaml21QIYJ0Uj92_-fVYb1W7BqFVjY,127936
scipy/integrate/_ode.py,sha256=UBdaILr3TUmCPs-pg32Eni12Gb0WKmyqVp_C5fTVHZQ,48074
scipy/integrate/_odepack.cpython-39-darwin.so,sha256=abE84idcnv-Cu-SMt1FoUXKpBUuUcpLNc6e6iln8GcI,106192
scipy/integrate/_odepack_py.py,sha256=ULRxBnl_FzZbmf_zfFMIK8r11puTTT37IzRy9rVONd8,10912
scipy/integrate/_quad_vec.py,sha256=zJrfx12UOsyI2bY26BZclLsxhv42xUEZ3ZSDcAcHaog,21234
scipy/integrate/_quadpack.cpython-39-darwin.so,sha256=6IY6BxSH-EViUumpTtuIFv8BiDh-5hUgTvbOcyA8Kbk,140960
scipy/integrate/_quadpack_py.py,sha256=RMY5JyhkDVESV4sZb2iUEBNezZ2Y-Z5dru5Bbx1k5Yk,53622
scipy/integrate/_quadrature.py,sha256=27OnvuGOs0s1j60mkpD33NkvfqEDyRkZZ2SdtsGshqE,65061
scipy/integrate/_tanhsinh.py,sha256=8bDtLU3cNHtHz2KZ_TDPEWlkaixUUeTZEfiCsTH2NJs,52905
scipy/integrate/_test_multivariate.cpython-39-darwin.so,sha256=pJWGEcVWtItdKU66_2Yur4sHKIBxsyI2PtI6e_n5JYM,33696
scipy/integrate/_test_odeint_banded.cpython-39-darwin.so,sha256=8bVSHhbY__FLVvxJj7cwwmkofoJRkXb3hwiS2t28cyo,128064
scipy/integrate/_vode.cpython-39-darwin.so,sha256=J_Ws2UQFvtZCWvC8REq4C_8jgPwiC0diXujhya9ij_A,195872
scipy/integrate/dop.py,sha256=EaxhHt4tzQjyQv6WBKqfeJtiBVQmhrcEIgkBzrTQ4Us,453
scipy/integrate/lsoda.py,sha256=hUg4-tJcW3MjhLjLBsD88kzP7qGp_zLGw1AH2ZClHmw,436
scipy/integrate/odepack.py,sha256=G5KiKninKFyYgF756_LtDGB68BGk7IwPidUOywFpLQo,545
scipy/integrate/quadpack.py,sha256=OAAaraeGThs2xYYWqKIOHiTe73Qh6zr8aoI1t8cqpnk,617
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-39.pyc,,
scipy/integrate/tests/__pycache__/test_tanhsinh.cpython-39.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=-pcKFE_LsIiMx-bGJWztpib8uhwe8AyETTM8yvv9If0,6284
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=kJWirYckJ7k4tfweg1ds-Tozp3GEhxTbuXfgSdeJw7k,6687
scipy/integrate/tests/test_bvp.py,sha256=Q3zw4r3lajNE9y2smIkAayRWrZ67r-yTuXODPeyvecY,20181
scipy/integrate/tests/test_integrate.py,sha256=U-TlhrTUh8BnQ7SlW9enL5gvO15QcGlmfDEHhnjhct4,24400
scipy/integrate/tests/test_odeint_jac.py,sha256=enXGyQQ4m-9kMPDaWvipIt3buYZ5jNjaxITP8GoS86s,1816
scipy/integrate/tests/test_quadpack.py,sha256=e6dBmLYXrV_veLdsypR0fTs8JW_rTTAlSC5ue3vy_JA,27983
scipy/integrate/tests/test_quadrature.py,sha256=_mQiQ1NizES6MYRUkNP1DlGssXp75aV61wajiSWEXuM,29999
scipy/integrate/tests/test_tanhsinh.py,sha256=fWXykp3jX-lE9HLeaTaGLY2iHQ8sHIWQnsTmxSADq2k,34195
scipy/integrate/vode.py,sha256=Jt60dcK-zXBgQF45FNRVtvyUbnkmaNWGbjX00I2mC3k,453
scipy/interpolate/__init__.py,sha256=AULPLFlB27t4jwYSXN_vojbsO4QF_UiN1kGVsxWeCSs,3530
scipy/interpolate/__pycache__/__init__.cpython-39.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-39.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-39.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-39.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-39.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-39.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-39.pyc,,
scipy/interpolate/__pycache__/_ndbspline.cpython-39.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-39.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-39.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-39.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-39.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-39.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-39.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-39.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-39.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-39.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-39.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-39.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-39.pyc,,
scipy/interpolate/_bspl.cpython-39-darwin.so,sha256=BPwEk-UetyvY1M-KxLFwCxTWficYdX5xIg-GrUs73kM,543808
scipy/interpolate/_bsplines.py,sha256=0UV-sSOfzePJI4wUP6R2rX4AfdOhocDRLhRDDokyJr0,75440
scipy/interpolate/_cubic.py,sha256=iuDbeuOhlDYUzGNpvvlnPv6xiG5_8pZIONqQ4b6nPiQ,38162
scipy/interpolate/_fitpack.cpython-39-darwin.so,sha256=NTUanhrJGox3w2rhKdP0lI9JRm2ssDoIjC6dAc6Kmac,121824
scipy/interpolate/_fitpack2.py,sha256=KFfeRremt7_PYekhXuH4rjlRrUvMw0pvKlxvgfHDFyE,89172
scipy/interpolate/_fitpack_impl.py,sha256=oTxX0ZBw1eChL2gKyVnEIOjQhbOdHv1JAFXPCivVi8A,28669
scipy/interpolate/_fitpack_py.py,sha256=HxdppqjgMmwwK-a2ZIoNSEjikbMlRLqWErKPdWoijSE,28064
scipy/interpolate/_interpolate.py,sha256=eBpiTbpC4_9O-7pokew59fmtazbOYN1Se__7d32HG3k,88259
scipy/interpolate/_ndbspline.py,sha256=rXABycf5_j8ESpY3DO_ysu76kxLKo1CawWUjbQzMSQk,12742
scipy/interpolate/_ndgriddata.py,sha256=Piz6T2dSyv7ozsX_sn3K5DdEIa18I9UJca9V2NrF4Uc,12092
scipy/interpolate/_pade.py,sha256=OBorKWc3vCSGlsWrajoF1_7WeNd9QtdbX0wOHLdRI2A,1827
scipy/interpolate/_polyint.py,sha256=jcB08oyPsO71j7omBYaz-q0UbGfnxMJPzUik6lMgkD0,34983
scipy/interpolate/_ppoly.cpython-39-darwin.so,sha256=z8-v-ASkJU2DNh3UiR82yCSX1Ay_5MROxBKnrrLskDE,397728
scipy/interpolate/_rbf.py,sha256=tBeBsMEe_NO1yxEv8PsX8ngVearEn1VfOyrCqEfr_Uc,11674
scipy/interpolate/_rbfinterp.py,sha256=bzuAuZpojP-cKCukD3jVekbQzZfHnrUT13Sex5pkKOI,19723
scipy/interpolate/_rbfinterp_pythran.cpython-39-darwin.so,sha256=PLwqU6fzAncPawgHJ0aTH5A3KcuV-GfKdmwzWmDXMlo,342480
scipy/interpolate/_rgi.py,sha256=zEKwwpQpvKU4j8NBc1SzPE61rdi_zACcZwPeqVTaPTk,31491
scipy/interpolate/_rgi_cython.cpython-39-darwin.so,sha256=ICKoo4crtzaz-MDYQwWa_LN55kcF0GbJuuOkYcSdQ7M,228512
scipy/interpolate/dfitpack.cpython-39-darwin.so,sha256=crTsX9mduAXhRdD1ERvBQ3fspcerV2J2yyTtRrJXulc,348144
scipy/interpolate/fitpack.py,sha256=VJP17JUH7I0hQhdGaOfhXpJkyUGYuKDfaZ0GGFdLE9o,716
scipy/interpolate/fitpack2.py,sha256=34oNI8q0UKW6kLh0iLGToTKmen1CsKHKiendex3Fp9k,964
scipy/interpolate/interpnd.cpython-39-darwin.so,sha256=9bAwwtNoHEvYIzmRe6fo-9zg1SHYSsW59mGoNK1Cvtc,395776
scipy/interpolate/interpolate.py,sha256=pmWxfOOtaAvMKJvkO8oLvMGBZp1cEDvUM9PJWg2Cl2g,963
scipy/interpolate/ndgriddata.py,sha256=F65cg9Tw-3LQy-G3V0YWFMN4yF23I6xOoQI3idK-sPg,677
scipy/interpolate/polyint.py,sha256=-KGJfScIoqD3mTuR7FKS8MKWaE4EtPzomfB0Zoaa4f4,712
scipy/interpolate/rbf.py,sha256=9AKQfUe99wmx8GaQoOd1sMo-o9yupBtvYBshimRqG9Y,597
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-39.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-39.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bsplines.py,sha256=XoOzxITldFfd5JxbGa2M_v6AL3USCNsAkq5mJZBBzKI,93848
scipy/interpolate/tests/test_fitpack.py,sha256=zkOUpis1bFPOiZSuBTcwOpM8TH8lYE37YhLlY_n_cdw,16057
scipy/interpolate/tests/test_fitpack2.py,sha256=fyNnCzCp2V-OQ8hHuRtgeSEcBlB102KFTu1HeOXm2ik,58726
scipy/interpolate/tests/test_gil.py,sha256=wt92CaxUlVgRGB-Wl2EuQxveqdARU8rZucD9IKl-pUE,1874
scipy/interpolate/tests/test_interpnd.py,sha256=n-jvOfEyyPrA46HH43xT-5mH7jN8iICRz6Hou80aPog,13675
scipy/interpolate/tests/test_interpolate.py,sha256=QkW9zZJzp-1sC-bBjbfUwpF9nsEEQhsyNXbKXCLm7U0,97533
scipy/interpolate/tests/test_ndgriddata.py,sha256=2q-eRB6cvvRjtBaeFjjZJJXkkYA_ILXSecOZueT0Z3Q,10980
scipy/interpolate/tests/test_pade.py,sha256=qtJfPaUxPCt2424CeYUCHIuofGGq0XAiyFCLYdkSMLg,3808
scipy/interpolate/tests/test_polyint.py,sha256=q6S4LFc0aJjbxm4H0rP1NFspQ9QHvzT9E4ZJVJd6ujM,36326
scipy/interpolate/tests/test_rbf.py,sha256=OitMk6wEbVeRS_TUeSa-ReWqR7apVez2n-wYOI08grg,6559
scipy/interpolate/tests/test_rbfinterp.py,sha256=-sc0cpgt-a8O-nFbEo-QLLg6ft8jcOdxwc7sCrk6_4Q,18518
scipy/interpolate/tests/test_rgi.py,sha256=lP35o1hjG_KuhR1waFJOAchvfL98Jj1lbjUIcWqqG3c,44770
scipy/io/__init__.py,sha256=XegFIpTjKz9NXsHPLcvnYXT-mzUrMqPJUD7a8dhUK_0,2735
scipy/io/__pycache__/__init__.cpython-39.pyc,,
scipy/io/__pycache__/_fortran.cpython-39.pyc,,
scipy/io/__pycache__/_idl.cpython-39.pyc,,
scipy/io/__pycache__/_mmio.cpython-39.pyc,,
scipy/io/__pycache__/_netcdf.cpython-39.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-39.pyc,,
scipy/io/__pycache__/idl.cpython-39.pyc,,
scipy/io/__pycache__/mmio.cpython-39.pyc,,
scipy/io/__pycache__/netcdf.cpython-39.pyc,,
scipy/io/__pycache__/wavfile.cpython-39.pyc,,
scipy/io/_fast_matrix_market/__init__.py,sha256=8okZpcBG5EjYz6kxS26Uxof9rk0YZcUb-3aT7dO_3SY,16876
scipy/io/_fast_matrix_market/__pycache__/__init__.cpython-39.pyc,,
scipy/io/_fast_matrix_market/_fmm_core.cpython-39-darwin.so,sha256=AzQMtyZm2xnm2pLE724oqdgduFSjpHHqSP0MJrDF-NA,2046384
scipy/io/_fortran.py,sha256=ZWR385RMYQtcjgv2S9CCaRwOHPKf1kzD8dzAIqw55WE,10895
scipy/io/_harwell_boeing/__init__.py,sha256=2iVxlj6ZquU8_XPA37npOdeHCXe8XbQrmMZO7k6Bzxs,574
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-39.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-39.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-39.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=ykWecU9ysrCFRfeIdctaELnIDQMaCt6PjGwkxpljNzw,8917
scipy/io/_harwell_boeing/hb.py,sha256=euxQyYRTvluzGUicNfEuyk4cOUCGLFCIs0r-8vjIZ-U,19177
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-39.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-39.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=0LxOjUewBj1Fwf7EOxMWZG_PdzMbVrFYMUeGgs23VII,2360
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=3eLwxTSg_Ebt2pjBLvZhpq8WUMjkFhM1lsTu_mgvDTI,2284
scipy/io/_idl.py,sha256=4oBvgwifLtx05eMKTNbYMfrOi1yi4poEM5scZb6J00w,27102
scipy/io/_mmio.py,sha256=-SCJh-M8Zmh-UbBs8mbyFJhGP3eCRLbAknB0s0zl-rQ,31872
scipy/io/_netcdf.py,sha256=dGNKBKWJ2ZcO5e5aQ1Z9oZW-n26clSweqv_bPhnSL78,39263
scipy/io/_test_fortran.cpython-39-darwin.so,sha256=VBbIZYXcCqjFkdjzojGX1jPrjy2hIS9RrLT7xYkIcsQ,76240
scipy/io/arff/__init__.py,sha256=czaV8hvY6JnmEn2qyU3_fzcy_P55aXVT09OzGnhJT9I,805
scipy/io/arff/__pycache__/__init__.cpython-39.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-39.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-39.pyc,,
scipy/io/arff/_arffread.py,sha256=iZgv9wiDI9oivXVd4lxhWgS1KPYS7sWvE9IV8bvlzPI,26560
scipy/io/arff/arffread.py,sha256=q8OPAnQ_eP4K4ZyspmXOeaR-KwpiVvEKTntVPEWew3o,1145
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-39.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=fTS6VWSX6dwoM16mYoo30dvLoJChriDcLenHAy0ZkVM,7486
scipy/io/arff/tests/data/missing.arff,sha256=ga__Te95i1Yf-yu2kmYDBVTz0xpSTemz7jS74_OfI4I,120
scipy/io/arff/tests/data/nodata.arff,sha256=DBXdnIe28vrbf4C-ar7ZgeFIa0kGD4pDBJ4YP-z4QHQ,229
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=01mPSc-_OpcjXFy3EoIzKdHCmzWSag4oK1Ek2tUc6_U,286
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=bcMOl-E0I5uTT27E7bDTbW2mYOp9jS8Yrj0NfFjQdKU,292
scipy/io/arff/tests/data/test1.arff,sha256=nUFDXUbV3sIkur55rL4qvvBdqUTbzSRrTiIPwmtmG8I,191
scipy/io/arff/tests/data/test10.arff,sha256=va7cXiWX_AnHf-_yz25ychD8hOgf7-sEMJITGwQla30,199009
scipy/io/arff/tests/data/test11.arff,sha256=G-cbOUUxuc3859vVkRDNjcLRSnUu8-T-Y8n0dSpvweo,241
scipy/io/arff/tests/data/test2.arff,sha256=COGWCYV9peOGLqlYWhqG4ANT2UqlAtoVehbJLW6fxHw,300
scipy/io/arff/tests/data/test3.arff,sha256=jUTWGaZbzoeGBneCmKu6V6RwsRPp9_0sJaSCdBg6tyI,72
scipy/io/arff/tests/data/test4.arff,sha256=mtyuSFKUeiRR2o3mNlwvDCxWq4DsHEBHj_8IthNzp-M,238
scipy/io/arff/tests/data/test5.arff,sha256=2Q_prOBCfM_ggsGRavlOaJ_qnWPFf2akFXJFz0NtTIE,365
scipy/io/arff/tests/data/test6.arff,sha256=V8FNv-WUdurutFXKTOq8DADtNDrzfW65gyOlv-lquOU,195
scipy/io/arff/tests/data/test7.arff,sha256=rxsqdev8WeqC_nKJNwetjVYXA1-qCzWmaHlMvSaVRGk,559
scipy/io/arff/tests/data/test8.arff,sha256=c34srlkU8hkXYpdKXVozEutiPryR8bf_5qEmiGQBoG4,429
scipy/io/arff/tests/data/test9.arff,sha256=ZuXQQzprgmTXxENW7we3wBJTpByBlpakrvRgG8n7fUk,311
scipy/io/arff/tests/test_arffread.py,sha256=7L9m9tLfHz8moV8wJyLs1ob_gxFBCBr3SDpZXW1fgng,13104
scipy/io/harwell_boeing.py,sha256=6cNioakGH8vMnjCt-k7W2vM5eq_L6ZMvnwpLB23KBoM,682
scipy/io/idl.py,sha256=WWbkHVJPlPTH4XBQmts7g4ei1UBlZFvR9fJ79poHwzM,599
scipy/io/matlab/__init__.py,sha256=YkLznYXgPaXmCNngcs9O9firIXLnM9Ez8iQC5luw2-Y,2028
scipy/io/matlab/__pycache__/__init__.cpython-39.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-39.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-39.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-39.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-39.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-39.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-39.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-39.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-39.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-39.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-39.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=5mtMzDwNmpSWeEk901SKqwN2tIXSNIN1FBpmZ2Pn3XY,1985
scipy/io/matlab/_mio.py,sha256=Bb4X8My32gDYfeZiRQuVzdJzjtGHJiwRYOxaQb3Z0Dg,12833
scipy/io/matlab/_mio4.py,sha256=xSIrZ1BbIoxtoQqa44pu5LgvlCclehfUuoWR4Q1jZ4M,20713
scipy/io/matlab/_mio5.py,sha256=28C22-ZpH782DqXyrpazkoEI6iCjnTcfXPWHZBstKB8,33580
scipy/io/matlab/_mio5_params.py,sha256=skRcKG70vOlVMSb1TO67LB5312zuOUSrcOK7mOCcUss,8201
scipy/io/matlab/_mio5_utils.cpython-39-darwin.so,sha256=xiQ8LfvqZpwL6n48jc3-z7c6LLwyrYEFWOoR_C50HYk,235568
scipy/io/matlab/_mio_utils.cpython-39-darwin.so,sha256=BdWqg1HAyBz5jM6JpYhsBVXmsAa5fBjOOpHotogeaEs,77656
scipy/io/matlab/_miobase.py,sha256=xw8D9CU6Aajk6-hXhtAW5GKMkbkSdJxTx17qogpSxCA,12962
scipy/io/matlab/_streams.cpython-39-darwin.so,sha256=8dgqHdAUV-OLrpx8sbRZR_101aaa4kzHeK10lWfR1vM,121288
scipy/io/matlab/byteordercodes.py,sha256=TP6lKr_4_0aUVqX5flFI_w_NabnJF3xvbm6xK4qWIws,611
scipy/io/matlab/mio.py,sha256=imPlshqcGZNEuWlzpYW-Y_JzUqcwdI9Z1SE3gjCzTWo,678
scipy/io/matlab/mio4.py,sha256=53boJCNzXr3bRewVn5xtBqp_gFvb1fEUZobx-cbxpqY,983
scipy/io/matlab/mio5.py,sha256=tcfrucXyoBq5OOSQWLpQvmlABq0ZhgKnnLK_-0ld-LQ,1217
scipy/io/matlab/mio5_params.py,sha256=bPjuNDH79SW5p-L4RFEXFiokiynE1rqolR26-qVH0RE,1294
scipy/io/matlab/mio5_utils.py,sha256=BrUSxwpJ2d32lW6Gjuuh5Sk7SeMQv-MS1r0sc-ZcaBo,661
scipy/io/matlab/mio_utils.py,sha256=JZP2mnyDKjHzABKHAZ5Nmxt9FdnlM1lUV-Qe4Uju2yk,558
scipy/io/matlab/miobase.py,sha256=JKUwT3HNlPzLFiigr3lPj9WB7yBx7mF8xitGuFwWu5E,764
scipy/io/matlab/streams.py,sha256=sh2KA6Wl-56ghy15v2P2tmIrH-Tb8bGnTp7z22XTx-8,585
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-39.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-39.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=FCHBAxeQZlhvTXw-AO-ukwTWvpN7NzmncBEDJ1P4de4,938
scipy/io/matlab/tests/test_mio.py,sha256=BcQlSLmQqqNv7CQa1HcLJYVp6OtlMig9FeliyRTc98Q,44810
scipy/io/matlab/tests/test_mio5_utils.py,sha256=eacgGg0TaQXOkG7iaeYovtWyjPgYCY50mHPoPjnHMTI,5389
scipy/io/matlab/tests/test_mio_funcs.py,sha256=fSDaeVPvCRBFzqjWtXR5xIv9UQ_yv6Y_Nl5D5u0HIGo,1392
scipy/io/matlab/tests/test_mio_utils.py,sha256=GX85RuLqr2HxS5_f7ZgrxbhswJy2GPQQoQbiQYg0s14,1594
scipy/io/matlab/tests/test_miobase.py,sha256=xH4ZOR_b25TJLyIGqYQdeSASpTi8j-oIkRcO4D-R4us,1464
scipy/io/matlab/tests/test_pathological.py,sha256=-Efeq2x2yAaLK28EKpai1vh4HsZTCteF_hY_vEGWndA,1055
scipy/io/matlab/tests/test_streams.py,sha256=dcirMJ5slCA3eIjB9VRcGG3U2htTtXL8BiYOLvHCfds,7406
scipy/io/mmio.py,sha256=jT06sWGxdylPF_jBjbrqV2H5TXVUa04R-38OGrN8DZs,569
scipy/io/netcdf.py,sha256=iDIpKlQcPWf2u-jIoYsqYx3a5oqWCy-54AcFW_muzU0,880
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-39.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-39.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=U8BS4PZxbnIzg8-GHYTXMDpHlKcDhu6-8GCbX6PVqho,7531
scipy/io/tests/test_idl.py,sha256=Q1ekSAxQdXN-MailSNDqaKHAQvyP9BxtOwGM3NpYyrw,20511
scipy/io/tests/test_mmio.py,sha256=GXrcNLv-2roKPaisWRyf6i9hG-EmmNkKqOX4HPx29WA,27874
scipy/io/tests/test_netcdf.py,sha256=8BpKkEm-G0zymAjpvMS5doLLORwhnX35nzPaod4vMxM,19404
scipy/io/tests/test_paths.py,sha256=3ewh_1yXujx3NIZ3deUjepFJgJDa5IHIugxupLDhHoU,3178
scipy/io/tests/test_wavfile.py,sha256=LLYFtOeL4vPdk7221TcQ_J3aVPVe9IfV16GyHCSoeAo,15647
scipy/io/wavfile.py,sha256=Jgz3Qi_6RXNphZVx6riCGK4qovdBbcnzI4726a0ex4I,26625
scipy/linalg.pxd,sha256=0MlO-o_Kr8gg--_ipXEHFGtB8pZdHX8VX4wLYe_UzPg,53
scipy/linalg/__init__.py,sha256=UOFZX4GCusrQjcaPB6NNNerhsVDe707BvlfE7XB8KzU,7517
scipy/linalg/__pycache__/__init__.cpython-39.pyc,,
scipy/linalg/__pycache__/_basic.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-39.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-39.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-39.pyc,,
scipy/linalg/__pycache__/_interpolative_backend.cpython-39.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-39.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-39.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-39.pyc,,
scipy/linalg/__pycache__/_misc.cpython-39.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-39.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-39.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-39.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-39.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-39.pyc,,
scipy/linalg/__pycache__/basic.cpython-39.pyc,,
scipy/linalg/__pycache__/blas.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-39.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-39.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-39.pyc,,
scipy/linalg/__pycache__/lapack.cpython-39.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-39.pyc,,
scipy/linalg/__pycache__/misc.cpython-39.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-39.pyc,,
scipy/linalg/_basic.py,sha256=bG3YlFR2vgoF8ijCkedBmEw4x0iAS_5-orpUdDxcE78,68914
scipy/linalg/_blas_subroutines.h,sha256=3nanVNwivmwbWRd42BNZB4G2lH7i5nYnsvO3gEohZQE,18134
scipy/linalg/_cythonized_array_utils.cpython-39-darwin.so,sha256=GAq1BhU0mHLZ-FPgxVdVdHE2h3kOjWebknfI8qJdgWc,529696
scipy/linalg/_cythonized_array_utils.pxd,sha256=OlWTbJt3gmdrfRFyx_Vz7GTmDTjr8dids5HA4TfC6R0,890
scipy/linalg/_cythonized_array_utils.pyi,sha256=HZWXvJdpXGcydTEjkaL_kXIcxpcMqBBfFz7ZhscsRNo,340
scipy/linalg/_decomp.py,sha256=ta_h9p6FoKFEe1pzV759Cinnrj00GsaHmGil6XIOf0Y,62177
scipy/linalg/_decomp_cholesky.py,sha256=aOKQKj0WG6j-UBUifPwoSx6NFmUa5RftayITRrD_tAw,11815
scipy/linalg/_decomp_cossin.py,sha256=N1TCrFf_-umaWn035E4CtxOBCkHROaFEhSqZLITLB3M,8973
scipy/linalg/_decomp_ldl.py,sha256=HYzVUNZgEyuC2ZoFOGneas8ZkhhOFzUGcapL3Pos_cE,12535
scipy/linalg/_decomp_lu.py,sha256=6KMcxOyCxLNFmzqh-DPmti8ck0gWQtSRdZmXUMMzzEs,12588
scipy/linalg/_decomp_lu_cython.cpython-39-darwin.so,sha256=JaZxgySvS6G_ukQwTYKvASnIPk5N0Z2cHAc4_MywE5M,224080
scipy/linalg/_decomp_lu_cython.pyi,sha256=EASCkhrbJcBHo4zMYCUl1qRJDvPrvCqxd1TfqMWEd_U,291
scipy/linalg/_decomp_polar.py,sha256=arzJ40FP1-TFsRvXPCP1qdNTsT60lkBcKBHfhB2JxxY,3578
scipy/linalg/_decomp_qr.py,sha256=n9241Aj2DY7RALMK4E22zApBppIMc-BV5P8mBOpML5g,13776
scipy/linalg/_decomp_qz.py,sha256=uH93in1ikPR-Wgi1g49EPm2XXuhKOWBzPUJEahCotx8,16330
scipy/linalg/_decomp_schur.py,sha256=yUUR-4mtWG0qjtz6UMhj5L0PMNGKLH5m12KElks4Gtk,10419
scipy/linalg/_decomp_svd.py,sha256=Egoy9LMjsNsykHqPp584LT43sVAyHS8LEWM1wUF7LDg,15616
scipy/linalg/_decomp_update.cpython-39-darwin.so,sha256=dQYnvZrI9uFf_RcMaXNtGQp1ikH8d4Y97osv8syqBzc,305312
scipy/linalg/_expm_frechet.py,sha256=efAQwON5vV4D_8NAe3EAM1NMNibQUlNZHjFmmp48Bs4,12328
scipy/linalg/_fblas.cpython-39-darwin.so,sha256=MEwWLS1oC44Ria5ulGqSCtrdRCoJy13MYaifBTVxlic,602256
scipy/linalg/_flapack.cpython-39-darwin.so,sha256=1oVyPnX2iNzaUpvDTmbKvUfuGhmtdMZSNtriWUbQKjQ,1866176
scipy/linalg/_interpolative.cpython-39-darwin.so,sha256=TFcOcPfhYm3sTZckjdo-tvH7ngKB0bkf_-26u-ahOuo,448064
scipy/linalg/_interpolative_backend.py,sha256=yycf_ceX0dgf7Usjvtaxmkm_cT-2jmEMBuWY6tJST2g,45192
scipy/linalg/_lapack_subroutines.h,sha256=E4T9vai7YJAJZ9HBMyGRpCm36NEufmTTdZDjWe-DwNA,239303
scipy/linalg/_matfuncs.py,sha256=oD7Ni2R7EQsJNRiQRt_LvM6cz-DCWOYEzUeOm1e5pUE,24331
scipy/linalg/_matfuncs_expm.cpython-39-darwin.so,sha256=RmwEb34UnAA9HJTTDqw7NhyLveGf3I2rUkSriAJ4Plk,450632
scipy/linalg/_matfuncs_expm.pyi,sha256=GCTnQ9X_CNNpadcYhDFhjL2WBhzfdnt0mkW1ms34cjY,187
scipy/linalg/_matfuncs_inv_ssq.py,sha256=THG87Ac9olliQ9tKjshCo1NRzb1QfgGHOOUomedP4eE,28059
scipy/linalg/_matfuncs_sqrtm.py,sha256=ijwi8Kqx8n4EIbTThMcyyJfDjjK51B_dCBM27tZdQLQ,6820
scipy/linalg/_matfuncs_sqrtm_triu.cpython-39-darwin.so,sha256=c1VhgVMx-DlZOzh7SA7ON7yDLAjnVlFeNYbAPUbjwQ8,209656
scipy/linalg/_misc.py,sha256=3IPq-LIQcxV7ELbtcgZK8Ri60YWbhpN_y7UYe6BKEgA,6283
scipy/linalg/_procrustes.py,sha256=aa5KcFwCM0wcwnLhwwBq_pWIMhfZoB5wIHY2ocS7Xc0,2763
scipy/linalg/_sketches.py,sha256=n6PEJILrFpzWhdf-sKFgGN-0elEwqvBlI0Z3H54tk0c,6145
scipy/linalg/_solve_toeplitz.cpython-39-darwin.so,sha256=dXKnDzkWasE9vMefnrm7ixX7SmtSowePY_kR_dGNKhI,247616
scipy/linalg/_solvers.py,sha256=q-bHb_WR4D3a_uOWpiD2zclBhotdxwPO8OwC4V0KGM4,28342
scipy/linalg/_special_matrices.py,sha256=NieLFLp1O_6BlgAx_fVRr2bVrqaFFS5VySRVNBFnIbc,36865
scipy/linalg/_testutils.py,sha256=IWA5vvdZ8yaHeXo2IxpQLqG9q54YIomHscYs85q9pd0,1807
scipy/linalg/basic.py,sha256=0uMJev4ZSqcrZ4FEV50FQyzf1U39QAhTu8gI_s_0R90,797
scipy/linalg/blas.py,sha256=WcuILhaA_wqcz2NJRl8gNabzec8Xi-kj4HeRS-EJhYY,11697
scipy/linalg/cython_blas.cpython-39-darwin.so,sha256=HHlOcR8Lesrjga8g7s3w52XLLR99i_J50lsM7HmqDQk,280624
scipy/linalg/cython_blas.pxd,sha256=DCPBxNWP-BvdT_REj6_a4TjUrNaf6sCq_XoxU3pEbfc,15592
scipy/linalg/cython_blas.pyx,sha256=DFCT-H2mDlf-KtVcTB4DQyCRSIIQjd1zB3r8NSUafrY,64918
scipy/linalg/cython_lapack.cpython-39-darwin.so,sha256=OQQ4qIWw1-BNIW0yRGqxG1Vw6H75rRKreQU7EED1clQ,665936
scipy/linalg/cython_lapack.pxd,sha256=Ld5hPwcYxpOPahFNsfNomsp0_DY8BfG-W8TmZxh-iYM,204556
scipy/linalg/cython_lapack.pyx,sha256=dLADFnGKlafqoLZOE7OqVmj2pzhWDNut0KJMzh_i9w4,706982
scipy/linalg/decomp.py,sha256=imZLuEFtV2WakBzX1DPiWCgUw00t4bEXyMyjtyQu_B4,838
scipy/linalg/decomp_cholesky.py,sha256=LfsMeb0QgOX2nLKgCsZCpi-mXBxGT596kPYVeRALok8,688
scipy/linalg/decomp_lu.py,sha256=1KQnoczngZjaNxs_CAP6-eUcyw2igK1PrmNHm1vhRlk,614
scipy/linalg/decomp_qr.py,sha256=QRjlkvSPo65naiTUDK823r6DnrcxDucOma6Z_DTLG0I,579
scipy/linalg/decomp_schur.py,sha256=6GtwTodRgqTY9tsmPpdKtIIgOGSEYub4_F2tmCYChvw,660
scipy/linalg/decomp_svd.py,sha256=HrJqbmgde7d7EWxCsa9XkS9QuWgPYMFOHiF4NcAL_Qg,631
scipy/linalg/interpolative.py,sha256=tPB5mfxVk_g0VSP1Y6YG4cqUkCSNYg7eomlu5KzhiO0,32251
scipy/linalg/lapack.py,sha256=1-XWvhL1N7R6vXQTturAC9CLEzoJSq0ata_molM_R2c,15667
scipy/linalg/matfuncs.py,sha256=G21MOYFXuqlDzWdBWC6FQ_nh5Hv0QwZaDDJ3PTwtHmY,883
scipy/linalg/misc.py,sha256=uxpR80jJ5w5mslplWlL6tIathas8mEXvRIwDXYMcTOk,592
scipy/linalg/special_matrices.py,sha256=tLbqSB71b5ucf8nFIAmkKmnFLEZbZk8IXYl4zZs_30g,771
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_misc.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-39.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-39.pyc,,
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=zia60-ir6RMT_f3dUwKZ32czTQR0GjmRQriQ7YBewfk,69951
scipy/linalg/tests/test_blas.py,sha256=_egnuCdKf89WuIkm45pl_02wMoHV3c4mvZ3uUV4NoWA,40842
scipy/linalg/tests/test_cython_blas.py,sha256=0Y2w1Btw6iatfodZE7z0lisJJLVCr70DAW-62he_sz4,4087
scipy/linalg/tests/test_cython_lapack.py,sha256=McSFDUU4kgCavU1u3-uqBGlzUZiLGxM5qPfBFgPTqdE,796
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=O1EKWxsYt6k1zMWjFlQhTndQVOhHsJlSm-bHfPMny1U,3840
scipy/linalg/tests/test_decomp.py,sha256=Gqb6312gdguygmQYUeqkN4dJg1ilWE6pcUoTqChOAWs,106888
scipy/linalg/tests/test_decomp_cholesky.py,sha256=FKAGOFEcx3Bh8NvZHoUjaDov-a6VpLdjSAswaxjACLY,7857
scipy/linalg/tests/test_decomp_cossin.py,sha256=Z9QpHHszBuZ-OorqILNK0Oly7sMvXNhbYLTZHNKd3YI,5955
scipy/linalg/tests/test_decomp_ldl.py,sha256=9h96PmHpoXIbjzc5nPxA3Dzw4575IelqxXw2aiNjabo,4944
scipy/linalg/tests/test_decomp_lu.py,sha256=i7K4zDx3PocMSPYJzaS0IiZuVRphC_CXzLreK1FNkIE,11186
scipy/linalg/tests/test_decomp_polar.py,sha256=5x5vz9rJE2U2nvo0kx6xMX5Z9OcnqxayPZvAd4dwsUQ,2646
scipy/linalg/tests/test_decomp_update.py,sha256=kPMpEe2ddl3rdEDhPlj-cdBL4BsPK3CAtf9g5k55vSo,68490
scipy/linalg/tests/test_fblas.py,sha256=Ykb7LKjbxPXAdJD-IkXMAsbUmXMAkku2FQCr-jlDTUE,18687
scipy/linalg/tests/test_interpolative.py,sha256=Y9yGVHR1OMZWHgrX_HmBx446TACjkARoxyHwT49iEuw,8969
scipy/linalg/tests/test_lapack.py,sha256=4dBJoJkgtXWnuof3Xx8UTBqWZ6lrg8h7NUeihxKIgsY,129349
scipy/linalg/tests/test_matfuncs.py,sha256=6b5wMGDvMI2PeimrjWastS3pZSE4f1-ETezFeJeyz6E,39926
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=Wd9T03zZRwX3M3ppkhYJiJbkWZ_xop4VKj57TjeozUs,3870
scipy/linalg/tests/test_misc.py,sha256=HP9jfKohbJIaKVcBqov9hAOHYk5dZck497-V5DMHe6E,76
scipy/linalg/tests/test_procrustes.py,sha256=WkNNarBf69izBmlOhu4-u0eWdzkSzYHQuDZh-w89fOU,6758
scipy/linalg/tests/test_sketches.py,sha256=FVEcNV43JteZZU7GDdBjtl-_alYDimxnjgKvpmtzVsI,3960
scipy/linalg/tests/test_solve_toeplitz.py,sha256=KuTAYh-8MRWjaHclgQuIaBBx8IBTGEzXgZnhM_gjWxo,4010
scipy/linalg/tests/test_solvers.py,sha256=degoX4OXSpo_6F59TyHcNdtcY3HCbkkGJRHldDfgdPs,31642
scipy/linalg/tests/test_special_matrices.py,sha256=7IbOPS0DyTC1zwEXbrjRr3NnctiTGlZsNRVqsJF17hQ,23596
scipy/misc/__init__.py,sha256=CdX9k6HUYu_cqVF4l2X5h1eqd9xUCuKafO_0aIY5RNE,1726
scipy/misc/__pycache__/__init__.cpython-39.pyc,,
scipy/misc/__pycache__/_common.cpython-39.pyc,,
scipy/misc/__pycache__/common.cpython-39.pyc,,
scipy/misc/__pycache__/doccer.cpython-39.pyc,,
scipy/misc/_common.py,sha256=4pb0UjMkG0GBlJ2IgZ4NDiu2vlPCxfL2r0BCOSpOFdE,11153
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=V67COWNbYuMJwdPMypUiimxSShtUXaq8RSop35sOiuM,619
scipy/misc/doccer.py,sha256=hUk7LlSlkTY28QjqyHv4HI8cWUDnZyg1PbMLvL3-Yso,1458
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/misc/tests/__pycache__/test_common.cpython-39.pyc,,
scipy/misc/tests/__pycache__/test_config.cpython-39.pyc,,
scipy/misc/tests/__pycache__/test_doccer.cpython-39.pyc,,
scipy/misc/tests/test_common.py,sha256=0h_qT7hwQnqx4Oc6ccvM-U79EkbXPq5LNlC3QSvR88M,833
scipy/misc/tests/test_config.py,sha256=j1Ppp6DCZy9wMxTmBEGxq4MScvsQXTQk7268EnNnPFQ,1244
scipy/misc/tests/test_doccer.py,sha256=V1B5Z-XfIQFiSyRNo3PXG-AQfToFmoQ1oOBGjxK2zmo,3738
scipy/ndimage/__init__.py,sha256=2dI3Sj1jF2AR1xSghzX4E5NFYxN9Z3-qd0a6YDRpPE4,4989
scipy/ndimage/__pycache__/__init__.cpython-39.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-39.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-39.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-39.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-39.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-39.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-39.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-39.pyc,,
scipy/ndimage/__pycache__/filters.cpython-39.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-39.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-39.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-39.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-39.pyc,,
scipy/ndimage/_ctest.cpython-39-darwin.so,sha256=-7lyesUS2f4T5nhMoWMlAChmPd6_6tmfD20uE0xGjLA,34144
scipy/ndimage/_cytest.cpython-39-darwin.so,sha256=mNxO2tlt87RqZKNI7WG6hP0xk5gcdVx-AYv_AwVFBqQ,79208
scipy/ndimage/_filters.py,sha256=ZqwtUcmk2UVLnQd71gdwQD8XbLHYXUYkKQRNl0iFJXI,65917
scipy/ndimage/_fourier.py,sha256=X-Y0EP59mH5ogqts58SpDhxA0dfqplwZQ8T0G6DzPos,11385
scipy/ndimage/_interpolation.py,sha256=xtG_a3pksNFF1tm7gl-2v36Zy8fxN4iPn2-j348Obdw,37023
scipy/ndimage/_measurements.py,sha256=7yn0c2ygTZm12oKUapXHT4r8MZ263ennI_qpEzXC8YM,56097
scipy/ndimage/_morphology.py,sha256=HKKP__gdrLNYDtp6J1qIzrcmpq7MYO7DpGHYAgyHMrk,94913
scipy/ndimage/_nd_image.cpython-39-darwin.so,sha256=cstRttMrm00o5rZsCbzOlQSIq8lUhPCZ9zsvQOQ-S5I,187824
scipy/ndimage/_ni_docstrings.py,sha256=Pxf50i8Wzrm2M70NkUrbdv901hsJ5XcRHVwyxHmXQJk,8505
scipy/ndimage/_ni_label.cpython-39-darwin.so,sha256=AKEN3IttKuZ2dgVRpKGc3M3BW0f5Or7iTJbQL4ZiQ0A,366792
scipy/ndimage/_ni_support.py,sha256=rO5ihuExCyN0o5mFUqU1ckg3pprTPpj8a1EZfIIdwqY,4646
scipy/ndimage/filters.py,sha256=cAv2zezrTJEm9JzKPV_pmXzZcgczCK_VaYJ4mdNW3FM,976
scipy/ndimage/fourier.py,sha256=gnifi4S_Epyu4DpNsebz4A5BKzBWoGf11FkXWeXsoqY,599
scipy/ndimage/interpolation.py,sha256=KzQNWvuqSrUfGcfe7gFSX9bHo7jVy76fErfjnpqbIaM,680
scipy/ndimage/measurements.py,sha256=xdSs52Y5RjURLP710iGURXWQFeS3ok4WjoYufKh9OeA,788
scipy/ndimage/morphology.py,sha256=yFWSo7o_7PuYq61WGQOCIgMppneNLxqhJocyN0bMsVA,965
scipy/ndimage/tests/__init__.py,sha256=LUFQT_tCLZ6noa1Myz-TwTfwRaSZ96zqJJUWNyMfb_k,395
scipy/ndimage/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_ni_support.cpython-39.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-39.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=JPbEnncwUyhlAAv6grN8ysQW9w9M7ZSIn_NPopqU7z4,294
scipy/ndimage/tests/data/label_results.txt,sha256=Cf2_l7FCWNjIkyi-XU1MaGzmLnf2J7NK2SZ_10O-8d0,4309
scipy/ndimage/tests/data/label_strels.txt,sha256=AU2FUAg0WghfvnPDW6lhMB1kpNdfv3coCR8blcRNBJ8,252
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=wZv9LUefK1Fnq__xemuxW2GDdRMdNN7gCqhWkdqZLZQ,3730
scipy/ndimage/tests/test_datatypes.py,sha256=tpCXBY_MH-NcCuytUVVnLbDy1q_3NN7hH245cpqhvsI,2827
scipy/ndimage/tests/test_filters.py,sha256=c-95IZ1gULMtWABykD2JJyYedpcsMPZwLomxb3ceeoA,94501
scipy/ndimage/tests/test_fourier.py,sha256=DlD_Eb1jZF_3y2wxi1IJaXI3566da9fnbY7jVtUZ42o,6664
scipy/ndimage/tests/test_interpolation.py,sha256=3kTKe5U76lDnEGTAWW9SzHyCnkbcr2KM1CluN_nUicc,54771
scipy/ndimage/tests/test_measurements.py,sha256=OMMWHW3zoziJ43lHfYJzr16KlQigcvcRf2LIJb9CrCU,48488
scipy/ndimage/tests/test_morphology.py,sha256=0qFGtsQkCn20vY9c4C10eeg44R4leNYO4F0BHAWSaNU,106687
scipy/ndimage/tests/test_ni_support.py,sha256=kuf8otEyIlGVPzcEPekRK7lfXI8bVEvB2_YF6ko7jzg,2472
scipy/ndimage/tests/test_splines.py,sha256=4dXpWNMKwb2vHMdbNc2jEvAHzStziq8WRh4PTUkoYpQ,2199
scipy/odr/__init__.py,sha256=CErxMJ0yBfu_cvCoKJMu9WjqUaohLIqqf228Gm9XWJI,4325
scipy/odr/__odrpack.cpython-39-darwin.so,sha256=fY-hOUXeWm4yvmLd4cUnBBldd0Jo1LeDyiVc6UO6vEU,257968
scipy/odr/__pycache__/__init__.cpython-39.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-39.pyc,,
scipy/odr/__pycache__/_models.cpython-39.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-39.pyc,,
scipy/odr/__pycache__/models.cpython-39.pyc,,
scipy/odr/__pycache__/odrpack.cpython-39.pyc,,
scipy/odr/_add_newdocs.py,sha256=GeWL4oIb2ydph_K3qCjiIbPCM3QvpwP5EZwEJVOzJrQ,1128
scipy/odr/_models.py,sha256=tfOLgqnV4LR3VKi7NAg1g1Jp_Zw8lG_PA5BHwU_pTH0,7800
scipy/odr/_odrpack.py,sha256=SaYqOX4MwAOAGBxK8ICbu1wH6vaBJCqF1RQoqCTIoiM,42401
scipy/odr/models.py,sha256=Fcdj-P9rJ_B-Ct8bh3RrusnapeHLysVaDsM26Q8fHFo,590
scipy/odr/odrpack.py,sha256=OlRlBxKlzp5VDi2fnnA-Jdl6G0chDt95JNCvJYg2czs,632
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-39.pyc,,
scipy/odr/tests/test_odr.py,sha256=ajJfXACR24a5cEqG7BiwAdoDYpAmvS1I6L7U3Gm-zL4,21011
scipy/optimize.pxd,sha256=kFYBK9tveJXql1KXuOkKGvj4Fu67GmuyRP5kMVkMbyk,39
scipy/optimize/README,sha256=FChXku722u0youZGhUoQg7VzDq0kOJ6MCohYcSQWSrg,3221
scipy/optimize/__init__.py,sha256=YUWDGxYsG4UmFsjNTMi5yWxB3mdLQUh9wbcnz4ATG0g,13108
scipy/optimize/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-39.pyc,,
scipy/optimize/__pycache__/_bracket.cpython-39.pyc,,
scipy/optimize/__pycache__/_chandrupatla.cpython-39.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-39.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-39.pyc,,
scipy/optimize/__pycache__/_dcsrch.cpython-39.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-39.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-39.pyc,,
scipy/optimize/__pycache__/_differentiate.cpython-39.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-39.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-39.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-39.pyc,,
scipy/optimize/__pycache__/_isotonic.cpython-39.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-39.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-39.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-39.pyc,,
scipy/optimize/__pycache__/_milp.cpython-39.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-39.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-39.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-39.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-39.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-39.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-39.pyc,,
scipy/optimize/__pycache__/_qap.cpython-39.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-39.pyc,,
scipy/optimize/__pycache__/_root.cpython-39.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-39.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-39.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-39.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-39.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-39.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-39.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-39.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-39.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-39.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-39.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-39.pyc,,
scipy/optimize/__pycache__/minpack.cpython-39.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-39.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-39.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-39.pyc,,
scipy/optimize/__pycache__/optimize.cpython-39.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-39.pyc,,
scipy/optimize/__pycache__/tnc.cpython-39.pyc,,
scipy/optimize/__pycache__/zeros.cpython-39.pyc,,
scipy/optimize/_basinhopping.py,sha256=ej5TxpHfW8-mH7rIsYtsaW9WGOj6FWmQUWab2YVlSNY,30691
scipy/optimize/_bglu_dense.cpython-39-darwin.so,sha256=7-X4LbN_zxrbRsd7olAmvyJBFZCIajjjh2AUF1G2_OE,287176
scipy/optimize/_bracket.py,sha256=o-ZowrYRDTItOlCut9k0B60sjRbGH6R4bv5ScG0_Q14,28614
scipy/optimize/_chandrupatla.py,sha256=SoGJwgIk3oWmRHG9EDgcG773fPdF1Z9SNJu2I3Hu2yA,23222
scipy/optimize/_cobyla.cpython-39-darwin.so,sha256=KOMcQVOw4WvgaI2MX-QIpBxTGTuQqafxv8SLF3dGZ5w,126432
scipy/optimize/_cobyla_py.py,sha256=bLw81_uD6zBTLybEfJUA46_OMdnTmXObhGZcvgBARss,10869
scipy/optimize/_constraints.py,sha256=_xlt1pkOpxXVJEj-yd_vkPfv20Pxt-us2yxlICngiY0,22854
scipy/optimize/_dcsrch.py,sha256=D5I9G4oH5kFD2Rrb61gppXFMwwz6JiQBYPvW3vbR5Gs,25235
scipy/optimize/_differentiable_functions.py,sha256=g-i-tnlS0RcWj6z8PF5cbNeYu_AjRjSbHmuewNN2juc,23665
scipy/optimize/_differentialevolution.py,sha256=wCLdSrATmzlpyOn3oeoIx-GR2malvM3QZYkhRMgroqo,83206
scipy/optimize/_differentiate.py,sha256=1cO7QcbxIs0g7gDl9Bo40X_c2PG13wWcYm4OpUHCGh8,30870
scipy/optimize/_direct.cpython-39-darwin.so,sha256=eNvxzRw52ZzPxOxa3_Lm45C6QPPYQAdeFR4ZjN9MOwU,68584
scipy/optimize/_direct_py.py,sha256=ShNGJHCdN02zGTQbBL5oEwxZ9yGH8dczXTsmnt1WJIg,11798
scipy/optimize/_dual_annealing.py,sha256=23UWd8CkGU02s5TaYoiu8h3Tv4GZmaVKgvGFo685Wlc,30346
scipy/optimize/_group_columns.cpython-39-darwin.so,sha256=OjtqfsAEiDjeOkczliGs9kMpRZq2Sz5mGFEfT9Bm-mc,97976
scipy/optimize/_hessian_update_strategy.py,sha256=PBnp8tf7hHcXb7uOz-GLJpoB79TCmdQM2IIOVX6ubI0,15862
scipy/optimize/_highs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highs/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_highs/_highs_constants.cpython-39-darwin.so,sha256=DQqrpUKJojsvIF8FwXXemrLwdMhANVv9V9i7z4_1hq4,56904
scipy/optimize/_highs/_highs_wrapper.cpython-39-darwin.so,sha256=U1A6Ay9Q0gx3NO5mdQuLKGhPqqcNtGqsYUONJJaMdtM,3615392
scipy/optimize/_highs/src/cython/HConst.pxd,sha256=ipav35Vt3T5POWpL3X0kGkXGMuDjfA8A61FPahnrRxI,5511
scipy/optimize/_highs/src/cython/Highs.pxd,sha256=1fwhSznVl2Vl_XyXyUTmX8ajygpeJKSgWbkpHiH6QZo,2147
scipy/optimize/_highs/src/cython/HighsIO.pxd,sha256=cnPDpEfuETXVLGdb4wgyVtQtKh5M2dd0rX9WidZG77U,705
scipy/optimize/_highs/src/cython/HighsInfo.pxd,sha256=TKvi5wZQ5DH4trIw29PhGWHmMnb8Cz_zjrTBDoodtCM,735
scipy/optimize/_highs/src/cython/HighsLp.pxd,sha256=ECXgv0gFOP2X12DPi1YWd_uybSAJ9hIll2SMUJ1DZjo,1106
scipy/optimize/_highs/src/cython/HighsLpUtils.pxd,sha256=eEFgoY_td38M5baXYvvlyFM72x2b1VU_lMFV3Y7HL-8,289
scipy/optimize/_highs/src/cython/HighsModelUtils.pxd,sha256=FzpoHqKLeMjwJCqM3qHWsxIZb69LNgfO9HsdwcbahZA,335
scipy/optimize/_highs/src/cython/HighsOptions.pxd,sha256=XsDO_rR9Y-0yxKSstRuv6VffEKh6tqIxIuef1UuanuI,3160
scipy/optimize/_highs/src/cython/HighsRuntimeOptions.pxd,sha256=MzjcGCorYJ9NbroJIyZDOM_v8RU4a1kjl1up4DPUicA,261
scipy/optimize/_highs/src/cython/HighsStatus.pxd,sha256=_pXo59wMcXeIw9mvZSwe9N77w3TaCVALe8ZghhPCF2M,339
scipy/optimize/_highs/src/cython/SimplexConst.pxd,sha256=hLhOZdBa0qfy_d8ZrXHbQiTfPx11V2xAiH-TGfTClEo,5018
scipy/optimize/_highs/src/cython/highs_c_api.pxd,sha256=LssK9RFO3D9eGRy2YjdncfnJQfKJ_cRHT6IxS9iV3lw,332
scipy/optimize/_isotonic.py,sha256=g4puoNqjJyDrJRoC0kvfG_I-0KNjeEfGpfZM5-Ltn48,6054
scipy/optimize/_lbfgsb.cpython-39-darwin.so,sha256=xJGD_xaQ11eNSDc22eOqbZ0IF9CxdzkfA_EEXq2MZgQ,144048
scipy/optimize/_lbfgsb_py.py,sha256=AR6PWfz5xgHBT6GEG_V5e7S9wqN8CKYDe9C_ShpT_uA,20718
scipy/optimize/_linesearch.py,sha256=-OwKJ52xl-pKeRM1kiCVgHGFkGrXW8BEGxUOiGcfEYc,27282
scipy/optimize/_linprog.py,sha256=EE4T4NoZoTtTbGvERlKilCLQs2uxxt65TgWnRSuUQME,29719
scipy/optimize/_linprog_doc.py,sha256=ejVGlwlW7xF5T7UkBbRpJ9-dBm6rcEAjXPbz-gWtdLA,61945
scipy/optimize/_linprog_highs.py,sha256=QbrJwka_Kz3xbpOZymQcm7NteXmzT9yxCskefrZNL58,17573
scipy/optimize/_linprog_ip.py,sha256=t43a8xJd9Ms8PSIFmdzmT6Pggner7l-Y5bkubWhlAI8,45785
scipy/optimize/_linprog_rs.py,sha256=5PhSblTUv5bgI9yW5BN1Rmy09gjZFA1tg1BXWxAKOQQ,23146
scipy/optimize/_linprog_simplex.py,sha256=I3hKTW_BFX0URJkByvqFL6bVBP5X84bq9ilXa2NxViY,24716
scipy/optimize/_linprog_util.py,sha256=3i_IjuXNBnz-F25qdW6VJLF8bKbG9_kOXCPwb1u2IHo,62749
scipy/optimize/_lsap.cpython-39-darwin.so,sha256=099z9rAgEGPS45QH_Zru1CwdK8Y6L7tWZI55WRXu3t4,53104
scipy/optimize/_lsq/__init__.py,sha256=Yk4FSVEqe1h-qPqVX7XSkQNBYDtZO2veTmMAebCxhIQ,172
scipy/optimize/_lsq/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-39.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-39.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=7u5B8LfUbv3ZRZ8DAZKuDTSNRfDEBmTsn25VZtMMsKk,5195
scipy/optimize/_lsq/common.py,sha256=nSiCudLnGfw1fWXXnsl5G7BslkYCMAMoC91QZOoVjq0,20523
scipy/optimize/_lsq/dogbox.py,sha256=97htRlr-Yt-********************************,11682
scipy/optimize/_lsq/givens_elimination.cpython-39-darwin.so,sha256=P-gMbppBvlAFcAgxy1LsvWP9dzn7k4CGQ_TTDqqf8VU,190120
scipy/optimize/_lsq/least_squares.py,sha256=XiGlnKJod4UV2YYXXuiNe4TJoh270b7fOFLjs8txxMY,39672
scipy/optimize/_lsq/lsq_linear.py,sha256=0Zpy7C0jdGLOE00NBohsu2iWq8hXMMI0FeA6oruZ-Co,15180
scipy/optimize/_lsq/trf.py,sha256=ElVHnB2Un3eaQ4jJ8KHHp-hwXfYHMypnSthfRO33P90,19477
scipy/optimize/_lsq/trf_linear.py,sha256=jIs7WviOu_8Kpb7sTln8W7YLgkcndv0eGIP15g_mC4g,7642
scipy/optimize/_milp.py,sha256=7Giiq-GsySyJzPQmWjwmbuSJyI4ZLPOmzkCbC2AHy9o,15187
scipy/optimize/_minimize.py,sha256=bGnVzGLCcPHNRgFeBhuvIeCRUo6rRkatHTcYijtv6_E,48221
scipy/optimize/_minpack.cpython-39-darwin.so,sha256=ksR5DgwjIs0GJTvC_n9Meqd_7DFtQexy8bprOj9w-EM,86656
scipy/optimize/_minpack2.cpython-39-darwin.so,sha256=eqa4b9tHzFgKmusOlU6eUXs5f0zYr1DKJBwLCGXyqrw,72584
scipy/optimize/_minpack_py.py,sha256=0lCQ_b1U8gFoaGs_6v_Mjq0QURPwyvS3L6x2LZWkOAA,44671
scipy/optimize/_moduleTNC.cpython-39-darwin.so,sha256=7zns996dtWOnW_unnb6MjN44n6fSuMPY6DqcCRhr_so,148344
scipy/optimize/_nnls.py,sha256=0iAi7_xT306p9r674t0Yf5w-Czvzu7ki8hHTbKJZvk8,5484
scipy/optimize/_nonlin.py,sha256=xoRMwh0Uk5pDVIGprgq3tbpJ2ZK-0jq4AUbmPTLDO_M,49886
scipy/optimize/_numdiff.py,sha256=BEZjmEEVCv34UHth_JvDTICwhlJWKY6UdGcE0YVOgnc,28720
scipy/optimize/_optimize.py,sha256=eOBZsdU17C6CwVEjjRMPEJiTBbv55Ts3VQ6F0_RY-Co,146575
scipy/optimize/_pava_pybind.cpython-39-darwin.so,sha256=m2WJ4CaMQJnThzTVcOHcgtOmUZ16lVaAMzdeRX6-UT0,155400
scipy/optimize/_qap.py,sha256=hFSa41-SnDya8Lro7UKViyx2Yz7ZpRfMKoaBTGNVqck,27831
scipy/optimize/_remove_redundancy.py,sha256=JqaQo5XclDpilSzc1BFv4Elxr8CXlFlgV45ypUwALyc,18769
scipy/optimize/_root.py,sha256=r64pq6PbKtTN6Fl4cfM61OErDQq4aBlHteZu__2RW6s,27806
scipy/optimize/_root_scalar.py,sha256=baTVT1Vi5ZeXLGxbxhbLkx4bRGA91uHfBzeiwcHUQpM,19595
scipy/optimize/_shgo.py,sha256=bVUz409huFf-M6q5Rdyiap-NPusAdWyCHbo0rBZoDoQ,62257
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-39.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-39.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=yzBQt3YjTcpw1PK4c_VJmi4CF94BZAiMMGDaTO1ai-8,50259
scipy/optimize/_shgo_lib/_vertex.py,sha256=I2TAqEEdTK66Km6UIkrDm2-tKpeJUuFX7DAfTk3XvUg,13996
scipy/optimize/_slsqp.cpython-39-darwin.so,sha256=2n0i-M6gi6294rpzpAXMqQay-R7xOsZ8MrrwKEn8lBs,89648
scipy/optimize/_slsqp_py.py,sha256=cHOtSPw8AP50yoTCc2yl3EzkDKW-wa5XYdkRwaBRdm4,19088
scipy/optimize/_spectral.py,sha256=cgBoHOh5FcTqQ0LD5rOx4K7ECc7sbnODvcrn15_QeTI,8132
scipy/optimize/_tnc.py,sha256=Y6rzgteDEKU0sxJ9UOcEsgzTQ3PD6x0WNg4k2IBO-r0,16908
scipy/optimize/_trlib/__init__.py,sha256=cNGWE1VffijqhPtSaqwagtBJvjJK-XrJ6K80RURLd48,524
scipy/optimize/_trlib/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_trlib/_trlib.cpython-39-darwin.so,sha256=rFLZZGXX_PgvMU75CdTdpIFdi1opaJNIkdAWvWUl-qM,320608
scipy/optimize/_trustregion.py,sha256=r4CGiKYFqNKWDFA_XT23_d4oqscIm5eSnWQNyno85Ps,10801
scipy/optimize/_trustregion_constr/__init__.py,sha256=c8J2wYGQZr9WpLIT4zE4MUgEj4YNbHEWYYYsFmxAeXI,180
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=690VxTb7JJ9RzGwa-LN2hASKlqQPmulyEDZA7I-XyLY,12538
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=5NiEruWnhYL2zhhgZsuLMn-yb5NOFs_bX3sm5giG7I8,8592
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=mWneWXy1bmte2nH_rq6VYPKXh9YlNIkiu3IG9uvRTck,25744
scipy/optimize/_trustregion_constr/projections.py,sha256=EO0uHULrNw8pm99vY-gd3pOFQEqrqk_13lVde9iUjTA,13169
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=EtAhRcEtSnGsEeEZ2HGEzm-7r0pnXMCgl9NemKWvdzg,22592
scipy/optimize/_trustregion_constr/report.py,sha256=_6b3C2G18tAgTstQSvqJbZVFYRxWKuUXFA1SAz95Y6k,1818
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-39.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=zVPxZDa0WkG_tw9Fm_eo_JzsQ8rQrUJyQicq4J12Nd4,9869
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=-UrTi0-lWm4hANoytCmyImSJUH9Ed4x3apHDyRdJg5o,8834
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=7tapj8clx8M7K5imwnTA4t-_Jh_cAYeu6efbGg4PbSU,27723
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=lbr947QQxz681HxTXEZZ0B6_2VNKiN85Inkz7XYhe4A,1070
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=HPyAfUzwu704yvplRMMMMvUKqBtC56gGUBvg218t-Zo,13798
scipy/optimize/_trustregion_dogleg.py,sha256=HS783IZYHE-EEuF82c4rkFp9u3MNKUdCeynZ6ap8y8s,4389
scipy/optimize/_trustregion_exact.py,sha256=s-X20WMrJhO36x3YEtxYepLqyxm1Chl7v8MjirrftUw,15555
scipy/optimize/_trustregion_krylov.py,sha256=KGdudJsoXXROXAc82aZ8ACojD3rimvyx5PYitbo4UzQ,3030
scipy/optimize/_trustregion_ncg.py,sha256=y7b7QjFBfnB1wDtbwnvKD9DYpz7y7NqVrJ9RhNPcipw,4580
scipy/optimize/_tstutils.py,sha256=Q5dZTgMzvonIb2ggCU9a35M8k_iV6v8hK4HDdKE20PQ,33910
scipy/optimize/_zeros.cpython-39-darwin.so,sha256=g-9lktgItaV7jt6XIjm7veVVS0gC7dDkEaMdTshnn4I,34400
scipy/optimize/_zeros_py.py,sha256=FLSkeAm2CoRkjLx37lKS6pMEvmlsZ8agt_ahA_rtwcM,52190
scipy/optimize/cobyla.py,sha256=6FcM--HbgtHfOZt5QzGCcmyH2wRmDA73UxN8tO8aIqE,619
scipy/optimize/cython_optimize.pxd,sha256=ecYJEpT0CXN-2vtaZfGCChD-oiIaJyRDIsTHE8eUG5M,442
scipy/optimize/cython_optimize/__init__.py,sha256=eehEQNmLGy3e_XjNh6t5vQIC9l_OREeE4tYRRaFZdNs,4887
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/cython_optimize/_zeros.cpython-39-darwin.so,sha256=C9GhMDxo8gCXFMLb8eVuNY4E1rWwdmomUrVF-cH9XEU,98248
scipy/optimize/cython_optimize/_zeros.pxd,sha256=anyu-MgWhq24f1bywI4TlohvJjOnpNpkCtSzpKBJSSo,1239
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=6Gc0l1q-1nlCO9uKrYeXFiHsbimRZzU3t6EoTa8MVvA,1118
scipy/optimize/lbfgsb.py,sha256=VHujkuUaSo6g_uQ2k5MqY1tvWUZrs9eqoZTAWCpRMY0,708
scipy/optimize/linesearch.py,sha256=HKsTaTIl0eE3ZZbPNf3T_ulRpsQVzj4MuQ3BROvBU14,781
scipy/optimize/minpack.py,sha256=I559Oh_EXey3U0Ixtz4lajjZeexPHMwnXS0aGX1qkY8,1054
scipy/optimize/minpack2.py,sha256=-GBMcSUKuDdYiS9JmGvwXMnzshmCErFE0E8G66nc9Bw,547
scipy/optimize/moduleTNC.py,sha256=qTEQ4IWtv_LT6fH3-iYmYNwrtrjG1gS4KFbZ73iDcd0,507
scipy/optimize/nonlin.py,sha256=Soe0x_9z4QyXdOGJxZ98pksET4H-mqauonpZk49WF-A,1200
scipy/optimize/optimize.py,sha256=uydjzFbjWgAN_lDMfOwjyGD7FEEhEbZIx3gBiUGKlL0,1240
scipy/optimize/slsqp.py,sha256=K9gVnto2Ol-0wzGisZXR9MxlGGFhjKIdhPfkUwkWLic,809
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_bracket.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_chandrupatla.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_differentiate.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_isotonic_regression.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-39.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-39.pyc,,
scipy/optimize/tests/test__basinhopping.py,sha256=QrDpRjbRnxgIDevxSovYFjC1UUrEr7g-goyzJHcFZms,18897
scipy/optimize/tests/test__differential_evolution.py,sha256=sVjn7FKKbMUq64fkTDgmjVNvidMxhvh_hEogG8biVrQ,68844
scipy/optimize/tests/test__dual_annealing.py,sha256=syotN4J2XhMSdTZaC95mlBRvzkh3Lce3mGtG05nH8dU,15173
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=9HFrqlU1OHGTHCgy_R9w2rJ5A5xlu_3QpGbnzQezqXM,11678
scipy/optimize/tests/test__numdiff.py,sha256=n0qb2yClsrDMNgrjvXqKZX_ww162ZF8C8_jbqvLrTiQ,31351
scipy/optimize/tests/test__remove_redundancy.py,sha256=gwakPkJo8Y8aRL4son1bp8USfwc9uMrLLnZFrDmfvxY,6799
scipy/optimize/tests/test__root.py,sha256=MvAzGJkaon4Hfk2BznRvFIVK05ezxezjvwmkEiEZFh8,4211
scipy/optimize/tests/test__shgo.py,sha256=mUOxM4itGBJ025EOjzlbA1I_ncj3WDkO0j1MRxlptvM,40291
scipy/optimize/tests/test__spectral.py,sha256=xh-4SMIAWkx_ND2nt7rGACy3ckfw_votfyfxMpQ8m2I,6664
scipy/optimize/tests/test_bracket.py,sha256=tzlXzMl_36yeDtQV_oU5YH8IBzAJWPfss9QLc6JuqIs,30579
scipy/optimize/tests/test_chandrupatla.py,sha256=04LrZHxJDpsSNDiZQg_0etOr1pChB-lP4_qLypTxJcA,30108
scipy/optimize/tests/test_cobyla.py,sha256=PcQCKsaEsyEqgEzZ_T-eC5kTtSdfNekvapf6LeoZPJU,5271
scipy/optimize/tests/test_constraint_conversion.py,sha256=vp-PUJNne1gnnvutl9mujO7HxnVcSMf5Ix3ti3AwDTI,11887
scipy/optimize/tests/test_constraints.py,sha256=03SN10ubXpgrNq9Z4DEpPSC6hTXznW-YUF-nxdaxSQ4,9408
scipy/optimize/tests/test_cython_optimize.py,sha256=n-HccBWoUmmBWq_OsNrAVnt4QrdssIYm4PWG29Ocias,2638
scipy/optimize/tests/test_differentiable_functions.py,sha256=UtUepS5cJTIHZrSrX8g-74lP-aodwwgGRU0ShbBwf5E,27019
scipy/optimize/tests/test_differentiate.py,sha256=Ide6nFAUD8KcWyQlV0SpF3PsmpZSPDlk7LI5LA1FEAs,15530
scipy/optimize/tests/test_direct.py,sha256=dUfsmTx9phFmlwv93UYgjYBoHh-iuWUrdc_KBn7jGlY,13152
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=czoYotEPSbAfcKhjjf3a9BNJ7i78c4pWzBKCNifuPAY,10115
scipy/optimize/tests/test_isotonic_regression.py,sha256=_qLmTpd3O9jI4qfFLYLxGiXAf3W5ON1xxro77Jr-GEM,7006
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=rpJbiCUfgJrjp-xVe4JiXjVNe6-l8-s8uPqzKROgmJQ,1137
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=44caMVc_OSIthB1SLFPK-k2m0mMWxN4pMiJ-cDnqnLU,3599
scipy/optimize/tests/test_least_squares.py,sha256=Ho5mgEuNB_t6Jj-M--wdN5e7SfgYnzXdZZZ3wOKETGQ,33951
scipy/optimize/tests/test_linear_assignment.py,sha256=84d4YHCf9RzjYDKUujQe2GbudkP8dtlSpZtMBwCf_Oc,4085
scipy/optimize/tests/test_linesearch.py,sha256=DVr7k5tkVpt2XkXwX2edFpRp1x15nfdcXFDK_Mb9XMk,10916
scipy/optimize/tests/test_linprog.py,sha256=eizplKYRvUKzcXzmvA5n6wNoFN7wzQpCGxowmJl7TTY,96989
scipy/optimize/tests/test_lsq_common.py,sha256=alCLPPQB4mrxLIAo_rn7eg9xrCEH7DerNBozSimOQRA,9500
scipy/optimize/tests/test_lsq_linear.py,sha256=E41vtYzwf9Px1QZpm1ShC9GU_sU2X-Cn9apfn5pku6M,10861
scipy/optimize/tests/test_milp.py,sha256=RDJe1CiL8-UMD8xqe4n2aVWp8qBe1hYufRx8qvad4wU,14553
scipy/optimize/tests/test_minimize_constrained.py,sha256=c6_cxRer5aG0cXpBH7MwOfIjkPeyG7d5-bVnn9y_IjM,26520
scipy/optimize/tests/test_minpack.py,sha256=EAarG7t3ucqklW4VWooF_7epPQcYdsocUmN5rjpuDMU,41341
scipy/optimize/tests/test_nnls.py,sha256=McLnzzUcdj7qANpv1Ui3QQ4XPJfZvvhPtVSDOxU7zFU,19194
scipy/optimize/tests/test_nonlin.py,sha256=IK7AjY9sWxEb6xwzE9IPnRi4VwQaCfTd9Wv0Pr7_lcs,18493
scipy/optimize/tests/test_optimize.py,sha256=Qe1JeRz6sxM6Ndcoou_EvxPSzdB0TY3X3BhsYJcHRPs,123372
scipy/optimize/tests/test_quadratic_assignment.py,sha256=zXttKYFREnrDhMExvBFNKzYb_77tFFsDlOPf-FP5XrA,16307
scipy/optimize/tests/test_regression.py,sha256=CSg8X-hq6-6jW8vki6aVfEFYRUGTWOg58silM1XNXbU,1077
scipy/optimize/tests/test_slsqp.py,sha256=KtqXxnMWsxI25GY-YT9BEZtgK9EkdLs_f5CRpXquiMQ,23258
scipy/optimize/tests/test_tnc.py,sha256=ahSwu8F1tUcPV09l1MsbacUXXi1avQHzQNniYhZRf4s,12700
scipy/optimize/tests/test_trustregion.py,sha256=HJtCc8Gdjznkzyn7Ei3XByBM_10pqv7VXgXBR9kCc8k,4701
scipy/optimize/tests/test_trustregion_exact.py,sha256=DnuS71T8CyVKWOP6ib7jB2PQEjNf3O5r1DQ4fQCJSi0,12951
scipy/optimize/tests/test_trustregion_krylov.py,sha256=DA169NkSqKMHdtDztMnlsrMZC3fnVlqkoKADMzGSWPg,6634
scipy/optimize/tests/test_zeros.py,sha256=UzJWUB9wBdKpOAN0IQEMm3sYjANg9xtpQzqs_NV4Saw,35691
scipy/optimize/tnc.py,sha256=5FKObWi_WSt7nFbOrt6MVkJQxZzCxZy_aStpnDV7okY,920
scipy/optimize/zeros.py,sha256=cL-uiCpCIb28_C5a2O8oGOGC_5t836mICzkKDoMMgZY,789
scipy/signal/__init__.py,sha256=Qi1hDJ8z3Zw5bdh3HK_Pj4H5aRgz7RML28_EqVC8ytY,13983
scipy/signal/__pycache__/__init__.cpython-39.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-39.pyc,,
scipy/signal/__pycache__/_bsplines.cpython-39.pyc,,
scipy/signal/__pycache__/_czt.cpython-39.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-39.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-39.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-39.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-39.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-39.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-39.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-39.pyc,,
scipy/signal/__pycache__/_short_time_fft.cpython-39.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-39.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-39.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-39.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-39.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-39.pyc,,
scipy/signal/__pycache__/bsplines.cpython-39.pyc,,
scipy/signal/__pycache__/filter_design.cpython-39.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-39.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-39.pyc,,
scipy/signal/__pycache__/ltisys.cpython-39.pyc,,
scipy/signal/__pycache__/signaltools.cpython-39.pyc,,
scipy/signal/__pycache__/spectral.cpython-39.pyc,,
scipy/signal/__pycache__/spline.cpython-39.pyc,,
scipy/signal/__pycache__/waveforms.cpython-39.pyc,,
scipy/signal/__pycache__/wavelets.cpython-39.pyc,,
scipy/signal/_arraytools.py,sha256=k3kHbl9RzcqsyftIYSFJZvJFL4zlcMAHyaRFUkFxOXY,8294
scipy/signal/_bsplines.py,sha256=84tNZ2SuCWbh810Xu4Q084zsLvBptHU7fNGV_gZTYhY,15731
scipy/signal/_czt.py,sha256=t5P1kRCM3iw3eCaL9hTgctMfQKezkqnjbghLjCkffQE,19445
scipy/signal/_filter_design.py,sha256=JgYGAcpX4uhomSfJU5zQ-25bomkD9PqnXfMovbg32Ps,186602
scipy/signal/_fir_filter_design.py,sha256=lcCVdgZytsIhVE1GdzksJ5sD2YPmD1D7EwvYOO52BIo,49381
scipy/signal/_lti_conversion.py,sha256=GDo7lUK9QLv7PCKoblyvHXaEVtYbuKTwAmJ3OAuy4Tw,16142
scipy/signal/_ltisys.py,sha256=g1c1oPuplyaQY0tfGGbq3XKfPUHNP0PW_G2AHoqJSLY,116354
scipy/signal/_max_len_seq.py,sha256=8QkMWoYY3qy3bCKfsuXaS93Bnb2zd-ue6j5i5-3_hi0,5060
scipy/signal/_max_len_seq_inner.cpython-39-darwin.so,sha256=UmQdfCsooqrK09aXtOnv-_-hnNWZoMgY0wVf-K1-Pw4,63104
scipy/signal/_peak_finding.py,sha256=d4y3__VSe9hPIueLZ_9xRKB9EnonvUOS6g1xp_WuxAY,48892
scipy/signal/_peak_finding_utils.cpython-39-darwin.so,sha256=5TsLFlioE0Y0a28NNHHnYFT_o2Z2Q8onyINXmuJCZq8,248912
scipy/signal/_savitzky_golay.py,sha256=mnltOfknWRlNiZmNLLy-zKTCrw6nZSdJPEvpGi0kv8E,13417
scipy/signal/_short_time_fft.py,sha256=jSd8xQrvHrJFyOVhcPJPduCThBvKXPLPuKcQDrOw5pE,73463
scipy/signal/_signaltools.py,sha256=38oXczH1v4GT4pGVuI1WIYzOFYLHhO66C-SxGbV5ums,157590
scipy/signal/_sigtools.cpython-39-darwin.so,sha256=7nrOqohbpx8h56d-OEUZEnCKXOi91NwWT81X7QG92ng,120864
scipy/signal/_sosfilt.cpython-39-darwin.so,sha256=1XGzVDbEH17a932IfaNKOjV8yNg4Meh_RVwPYQt6WN0,243408
scipy/signal/_spectral.cpython-39-darwin.so,sha256=uJBYGGC0pcvwHWBw2V778PUPG5qNe5UlEPpsGc7xbVU,63592
scipy/signal/_spectral_py.py,sha256=xRwdztzKYeYv0xIGYfGdxVeW3-DN5L0XJYFlWZjWm7o,78406
scipy/signal/_spline.cpython-39-darwin.so,sha256=pQd5W0ZvHSpsk-t5zZ6z2rXk1iRq77riANJVDELkepI,69128
scipy/signal/_upfirdn.py,sha256=ODSw2x1KHXN0vdKHm4vnovZxkoafcwIdUek0N8Edu5g,7882
scipy/signal/_upfirdn_apply.cpython-39-darwin.so,sha256=j8txRNE85DQgfFeaSmW9TCNYwh16HCNvh0EIixVn2RU,298288
scipy/signal/_waveforms.py,sha256=Bm5WOBhk1nXwK0A6yFVTY7tCCv6trdrUjje_xmM878Y,20523
scipy/signal/_wavelets.py,sha256=NzmN785S0xFdgFhC4Lv52BKrvw3q3wtyVZdCditpDG8,16095
scipy/signal/bsplines.py,sha256=xpwI33IQDzkH6S5o8ZxDtNj40dDD1G_tkpG4MaMMxQ4,738
scipy/signal/filter_design.py,sha256=TRo01JzmAh6zpgVgZi_8pHLPM2DKo9fA9yDXpU5AOCM,1471
scipy/signal/fir_filter_design.py,sha256=m74z7fwTgiYFfHdYd0NYVfpUnDIkNRVCG8nBaOoPVZ8,766
scipy/signal/lti_conversion.py,sha256=fhyTsetZE9Pe57f9DeBdOIZwc71Nxw7j2Ovn6m7w2W0,707
scipy/signal/ltisys.py,sha256=E5t7vHjsj09EYmpd27aqtRvT8E8sDpH-5YOgcmeqypI,1146
scipy/signal/signaltools.py,sha256=ZnV0ARj_8YPUZ7cIxpM2Ko5yuOkW7Ic-JxN5uLmGcj8,1179
scipy/signal/spectral.py,sha256=m_Q-gzRpT6e_w2kIBFKPBLuDVj5If5zfVWbAViAQJsk,723
scipy/signal/spline.py,sha256=iisoUmgbyuuEukQjBz99HM3SYao7j1ZsXXmtE-wo5cU,810
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/signal/tests/__pycache__/_scipy_spectral_test_shim.cpython-39.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_short_time_fft.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-39.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-39.pyc,,
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=qkEcaCK7_jPHA7sellidJJs6rS6wo9xO9f5YkFdqBOQ,19995
scipy/signal/tests/mpsig.py,sha256=DHB3eHB0KYA-E0SBebKG36YLk-T5egbwwryne3RwIHM,3308
scipy/signal/tests/test_array_tools.py,sha256=J9Mr5DtqmhiTReWvsk3YclL6Cnv32bDuklBnw2zprJY,3632
scipy/signal/tests/test_bsplines.py,sha256=7nnnsABF-uwKj13_Vq-CSbZJeIqx22j4yYySw83Q40o,8855
scipy/signal/tests/test_cont2discrete.py,sha256=3IkRfgGlgnX7X0bERpExPAxAkcGK0h6Ovy6GyrhnYS8,14605
scipy/signal/tests/test_czt.py,sha256=3HxxWwOWIrIc0GC-K5h6f0NRjkLrWRA5OhoB5y0zbw0,6993
scipy/signal/tests/test_dltisys.py,sha256=f4wDe0rF_FATRWHkHddbPDOsFGV-Kv2Unz8QeOUUs-k,21558
scipy/signal/tests/test_filter_design.py,sha256=whn5g9GR7BcsFjSMJyCMQFkrSWJoGSr9bhwEwmOGKP8,193782
scipy/signal/tests/test_fir_filter_design.py,sha256=77Dt5heM2m9QTQ9VUZTeeSWnTi9cOjFbL-51CfNX-_8,29941
scipy/signal/tests/test_ltisys.py,sha256=MbFugdbcNFZuzxcpjcVldhpaR64E0AaOg0qEWgPSMQQ,45208
scipy/signal/tests/test_max_len_seq.py,sha256=X9oyCvW0Ny8hOAVX22HmKaMgi2oioe1cZWO3PTgPOgw,3106
scipy/signal/tests/test_peak_finding.py,sha256=03S223wQ6xcJ_VyO6WCxthrFjWgatAmGKm6uTIZOlfk,33863
scipy/signal/tests/test_result_type.py,sha256=25ha15iRfFZxy3nDODyOuvaWequyBpA42YNiiU43iAc,1627
scipy/signal/tests/test_savitzky_golay.py,sha256=hMD2YqRw3WypwzVQlHwAwa3s6yJHiujXd_Ccspk1yNs,12424
scipy/signal/tests/test_short_time_fft.py,sha256=h1xMjXJKr9HO1FEElm-D60uKPjPOckL7XOWhGH-fKtY,34474
scipy/signal/tests/test_signaltools.py,sha256=rW7rMh50nQxlBWeQW104HUQWI8x6z9Me4C3Eruh0tk8,141443
scipy/signal/tests/test_spectral.py,sha256=9IwUmrhRIynmcuCr-24LMH3HN9rcf2-49tP6bixkFEg,63775
scipy/signal/tests/test_upfirdn.py,sha256=i3EjQKnwS6FRRRPPzwl1B_zWsQ20Dfa_6WUUYH8I3xM,11240
scipy/signal/tests/test_waveforms.py,sha256=sTT0DeOER5U9h8Xp54VGvGlbtcxhp_wjGNQXw1yOaGM,11975
scipy/signal/tests/test_wavelets.py,sha256=BurB2_FZ9rnLVJVhItmaueAUqlnmXR2POtFAJ-h3FLU,6721
scipy/signal/tests/test_windows.py,sha256=tLnQi4VyekCfhV3Bn1mCY9pCVcDH6TbuYa7yiUI8rak,40990
scipy/signal/waveforms.py,sha256=HHwdsb-_WPvMhFLAUohMBByHP_kgCL3ZJPY7IZuwprA,672
scipy/signal/wavelets.py,sha256=ItCm-1UJc8s9y-_wMECmVUePpjW8LMSJVtZB-lFwVao,612
scipy/signal/windows/__init__.py,sha256=BUSXzc_D5Agp59RacDdG6EE9QjkXXtlcfQrTop_IJwo,2119
scipy/signal/windows/__pycache__/__init__.cpython-39.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-39.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-39.pyc,,
scipy/signal/windows/_windows.py,sha256=F-9DNB-71WE3WQOxVfNESgmc4gG21rDFgD631Y9-E78,83607
scipy/signal/windows/windows.py,sha256=OztcTMqgFMLguY9-hVUvSSPMYY4GYkbrFvtsRcktxC8,879
scipy/sparse/__init__.py,sha256=WClFuFd1byUOWhYZ6ZrjBsnKTwXEvjUJpVoMzbAvvv4,9272
scipy/sparse/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/__pycache__/_base.cpython-39.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-39.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-39.pyc,,
scipy/sparse/__pycache__/_construct.cpython-39.pyc,,
scipy/sparse/__pycache__/_coo.cpython-39.pyc,,
scipy/sparse/__pycache__/_csc.cpython-39.pyc,,
scipy/sparse/__pycache__/_csr.cpython-39.pyc,,
scipy/sparse/__pycache__/_data.cpython-39.pyc,,
scipy/sparse/__pycache__/_dia.cpython-39.pyc,,
scipy/sparse/__pycache__/_dok.cpython-39.pyc,,
scipy/sparse/__pycache__/_extract.cpython-39.pyc,,
scipy/sparse/__pycache__/_index.cpython-39.pyc,,
scipy/sparse/__pycache__/_lil.cpython-39.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-39.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-39.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-39.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-39.pyc,,
scipy/sparse/__pycache__/base.cpython-39.pyc,,
scipy/sparse/__pycache__/bsr.cpython-39.pyc,,
scipy/sparse/__pycache__/compressed.cpython-39.pyc,,
scipy/sparse/__pycache__/construct.cpython-39.pyc,,
scipy/sparse/__pycache__/coo.cpython-39.pyc,,
scipy/sparse/__pycache__/csc.cpython-39.pyc,,
scipy/sparse/__pycache__/csr.cpython-39.pyc,,
scipy/sparse/__pycache__/data.cpython-39.pyc,,
scipy/sparse/__pycache__/dia.cpython-39.pyc,,
scipy/sparse/__pycache__/dok.cpython-39.pyc,,
scipy/sparse/__pycache__/extract.cpython-39.pyc,,
scipy/sparse/__pycache__/lil.cpython-39.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-39.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-39.pyc,,
scipy/sparse/__pycache__/sputils.cpython-39.pyc,,
scipy/sparse/_base.py,sha256=yXHwyNvhZYQ4JN7AxHwOR2zZPRzjBPzet_8Lv5WeKVE,52557
scipy/sparse/_bsr.py,sha256=9oCEOHbJgEr9ZUFYj8ZMBeAxo3RDflKjjrbNJ_WSKas,30274
scipy/sparse/_compressed.py,sha256=65gowaNA-Mbk1aVOFAcLUyI5cvEt6_8ciWqJ_Rh_I9o,53352
scipy/sparse/_construct.py,sha256=S8avkP1bHGA5Hrufj2IldPqYXK1ls0GRUBdIRBpGfWw,47179
scipy/sparse/_coo.py,sha256=fpSfXXyGI78ldaAYwvPuPpl3_YUmjSfB-F1vqWtB-QQ,31761
scipy/sparse/_csc.py,sha256=oMNfti0VZ-OKJi-5THPcQCrj-vWFS3heJoGWUCyJ-EM,11057
scipy/sparse/_csparsetools.cpython-39-darwin.so,sha256=JY2K9AN95xlP1pkT6Rm6jioCKOCJ4YUiKqgb_mAUfXk,680080
scipy/sparse/_csr.py,sha256=nM2lgWRujXz_PhoinsooCfn0iqkzGS9aNm-Mapi3bus,15675
scipy/sparse/_data.py,sha256=CbZVzMgio3OLAlLl2_1SlGHO7A2oXcdpAhKu1VgTlTI,17219
scipy/sparse/_dia.py,sha256=w0Uia5COvF4KSr4ZCEWCgLRtKGEhy4Zq7D69AcdhoYw,18905
scipy/sparse/_dok.py,sha256=Z3HYo08LURu9MnfGXRCsib81SeXXn3M8wDhkWFUIDdE,22201
scipy/sparse/_extract.py,sha256=iIRSqqVMiXfiacfswDCWXTjZCFfRvOz1NFicLUMHSl4,4987
scipy/sparse/_index.py,sha256=c_Wt3XdFl9Zd6bAnfZ-pOCYHZ6VaB1a1duIh9xvYO50,13279
scipy/sparse/_lil.py,sha256=Di05AOTmtVyWts-2yDM8kGOrADySDtR48Z99n8w2gGE,20627
scipy/sparse/_matrix.py,sha256=cT7Piq0NYzvRouy3HksG7d063HTjRlauBheAAT9PzCI,3081
scipy/sparse/_matrix_io.py,sha256=dHzwMMqkdhWA8YTonemaZmVT66i3GiG46FBcsIDBbAY,6005
scipy/sparse/_sparsetools.cpython-39-darwin.so,sha256=-DYLKSFxZiA_SsX7VkJrjC28xYzB1cPqzxSqaFPhbOs,5158632
scipy/sparse/_spfuncs.py,sha256=lDVTp6CiQIuMxTfSzOi3-k6p97ayXJxdKPTf7j_4GWc,1987
scipy/sparse/_sputils.py,sha256=o3u434vbhJaoOE0ixhQQXJ_0T7ZqC-hmt5RmgFPm048,14545
scipy/sparse/base.py,sha256=8Yx-QLKSRu9LJjgG-y8VqsRnsjImB2iKoJFxTgKGFsI,791
scipy/sparse/bsr.py,sha256=CsYirxoLqHwBiEyNbOgGdZMx4Lt3adKZ-7uVv1gpzCY,811
scipy/sparse/compressed.py,sha256=rbaz4AoTJvNnfnwEx4ocDXlkHJPOxe9DzqxCcJoHY2g,1009
scipy/sparse/construct.py,sha256=i9lHBSRsDkvoNCbF9b7mZ0C2fHCjKU5CKCE30c-CxMc,925
scipy/sparse/coo.py,sha256=VRF6kaYsVtyprwYrEuy1gRcCU5G7xsKyY0L1zJ_9JiQ,844
scipy/sparse/csc.py,sha256=EV_LxYjPiRsTV6-J8kUefNna-R0tdI5uBt9Fj_XWlwc,609
scipy/sparse/csgraph/__init__.py,sha256=VbNYhqSQ5ZPIPjU3Q9Q9MKTH1umiVu11GOjXNa1Cx68,7753
scipy/sparse/csgraph/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-39.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-39.pyc,,
scipy/sparse/csgraph/_flow.cpython-39-darwin.so,sha256=ADKhr542fdnzIHxVyGFvTweJOwBchE6XOQ3RGKaBU-8,301640
scipy/sparse/csgraph/_laplacian.py,sha256=n5iodxzmVtvbpcFLld-y-ZG3539uebImpMfIfnMhMck,18209
scipy/sparse/csgraph/_matching.cpython-39-darwin.so,sha256=ncP4n62JTSV5aq1gsExcT3sAM23fejutdYOxaJHdgZs,319368
scipy/sparse/csgraph/_min_spanning_tree.cpython-39-darwin.so,sha256=f3FJEwFcO2Ky_Ke2_dYTCzufz23XesvTZwQqtouh3Pk,210696
scipy/sparse/csgraph/_reordering.cpython-39-darwin.so,sha256=nhKHOVbnRkSOEFYUaKkNKat7RndXq8AFaeeR1UrJNSA,266752
scipy/sparse/csgraph/_shortest_path.cpython-39-darwin.so,sha256=lgbE8_q2Zju8qsPNZw0E7U-K85PEOmY2pjHnBr2omds,463856
scipy/sparse/csgraph/_tools.cpython-39-darwin.so,sha256=u5F_r6p_cCkOmzRsW2VPO1BY-GOTlvrZdM95NBtfArk,193776
scipy/sparse/csgraph/_traversal.cpython-39-darwin.so,sha256=7vzahUbzhi8Z1o1e3QCjIu6SNmy9-JkF2nnKlZkzUNk,552232
scipy/sparse/csgraph/_validation.py,sha256=VQl6Aj3ns7AhLe3BDKp0-tRUXSzXOeD32wQ1eN7xnek,2476
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_pydata_sparse.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-39.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-39.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=a2HZjm7HsC0STqiDnhN6OJL4yIMcM28VNVtMXDI2BqE,3948
scipy/sparse/csgraph/tests/test_conversions.py,sha256=KJ6jEAYl5C8APyH_WE5I1M8qGgxOyjGtNPf9rt4RYCo,1856
scipy/sparse/csgraph/tests/test_flow.py,sha256=BXhx0qBT3Ijy9all5OhNVNVzMbdTPySQuaZ1ajK6DTs,7420
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=6fDEldaGM_gEZk-NMHaeQMKjZRnz3J7R5kWqHhfchY0,10990
scipy/sparse/csgraph/tests/test_matching.py,sha256=MkSKU_9_IIhRnhp5sbRbB8RYqVe_keS4xqhDVvV3EhM,11944
scipy/sparse/csgraph/tests/test_pydata_sparse.py,sha256=eoiFT4O_myDq2hVHM3A2qkwL5t8hv3XwRLhXwC4ZmHE,3601
scipy/sparse/csgraph/tests/test_reordering.py,sha256=by-44sshHL-yaYE23lDp1EqnG-72MRbExi_HYSMJEz8,2613
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=RmRAk_RxMo3C9do0f01DsHSPyDUVEUZXuq4h6aALrDo,14441
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=7Zcbj_87eeAkm6RetgeO0wVp1EOIEjGxJLuGtw_H9qc,2168
scipy/sparse/csgraph/tests/test_traversal.py,sha256=UNTZXJ9bjDHcji_vUa1Ye5Kbp6xLfyHBG9LusToGUSY,2840
scipy/sparse/csr.py,sha256=9UrWUoq5-hSl9bcaVeWxN4tmPJisTQ_6JiISCyrlMCw,658
scipy/sparse/data.py,sha256=qGDAuAvTASgQ7wXXZ9t2JPp0rNBNVxObTTzXNHDRSEo,573
scipy/sparse/dia.py,sha256=0y5_QfvVeU5doVbngvf8G36qVGU-FlnUxRChQ43e1aU,689
scipy/sparse/dok.py,sha256=LMnaLFd266EZ3p4D1ZgOICGRZkY6s7YM0Wvlr6ylRn0,733
scipy/sparse/extract.py,sha256=6qT2PNOilsEhDWl6MhmgpveIuQr4QCs3LATwIrBroOQ,567
scipy/sparse/lil.py,sha256=BbnMgvzMi33OqmBNYF_VDPeju2RcRs9OyZUUU3aZHcc,734
scipy/sparse/linalg/__init__.py,sha256=_2NSGBqWo-MaV_ZiFDzXRYTM9eW8RfmtSWVp4WMESyw,3999
scipy/sparse/linalg/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_special_sparse_arrays.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-39.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-39.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=YxlWZfj2dxiZrFLL6Oj6iWKEuC6OHXdRVRf9xCU_Zoo,1991
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-39.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-39.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=ASCr6jhvN8hgJCEg9Qq685LXKJuGTvFQCZtUwzWphDk,3912
scipy/sparse/linalg/_dsolve/_superlu.cpython-39-darwin.so,sha256=JU9oacZ6K2ZBgUSX9krydPJtUKPajJ1KslM9RWlXXAI,409296
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=Iro6NQavwUGTmib9d3UOPBQAXXCVpplzfCiqRDS6nh0,26486
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-39.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=632NbRmJm2-8vbQ6g9pFiMsApZ01tIGveNfP0BUjVXo,27784
scipy/sparse/linalg/_eigen/__init__.py,sha256=SwNho3iWZu_lJvcdSomA5cQdcDU8gocKbmRnm6Bf9-0,460
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=3N36RCFHqkYraaY7Fc7WoN-w9_7c1cG0QnlWYAJaroA,20239
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=3_mPNg5idszebdDr-3z_39dX3KBmX2ui1PCCP_hPF24,15605
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=CSZWb59AYXjRIU-Mx5bhZrEhPdfAXgxbRhqLisnlC74,1892
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=zDxf9LokyPitn3_0d-PUXoBCh6tWK0eUSvsAj6nkXI0,562
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-39-darwin.so,sha256=nzY7nphIgeIYyzoCIYUlLGVujscJAMS-EwvSVaErxE8,511024
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=BSkXtfwvmUtmBejugJkE2LOPeGtV-Ms7TxXHIpD_Rx8,67401
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=R5FfNhm1CZNVMiP_ldOp5x_0pzpwCJlO68FPW_pR8vw,23750
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=E5JEPRoVz-TaLrj_rPm5LP3jCwei4XD-RxbcxYwf5lM,420
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=CdmO8VQrARiE1i8VJvE4O0tYytbzQCzDIf3eo1sWq6g,41905
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=TVAhSqfKVm-T05Nx-eIJfMMyf8P-XEyZv_r9YSrHuZo,23813
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-39.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=NfOhlR0H8O7U7rZiJ4PGfMzM0_NqPg4WrSN6VBHMARQ,36168
scipy/sparse/linalg/_expm_multiply.py,sha256=enIS-h-6F6UQ6SQeR57bH8MYbM4XzwQv5dVqlWVqhJU,26312
scipy/sparse/linalg/_interface.py,sha256=drcxlR1TUiZ1sEat2ke6bh62DPIe888Xd1QagqHMlq8,27979
scipy/sparse/linalg/_isolve/__init__.py,sha256=Z_eQUYbe6RWMSNi09T9TfPEWm8RsVxcIKYAlihM-U-c,479
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=j2JVJBMs8u72hwF0jueRIfkJlS4ZtUZHt0TXYzWXcUY,16212
scipy/sparse/linalg/_isolve/iterative.py,sha256=T2ebi650XYuxLcE90_vvNhnmDKNn4yCMEHy2fQyqFMM,35768
scipy/sparse/linalg/_isolve/lgmres.py,sha256=_HXq4vrLuoo2cvjZIgJ9_NJPQnpaQNoGcrUFQdhgQto,9159
scipy/sparse/linalg/_isolve/lsmr.py,sha256=ej51ykzoqpWvyksTFISRN-lXce7InPpqyDT4N42QEpY,15653
scipy/sparse/linalg/_isolve/lsqr.py,sha256=mJADMPk_aL_lf57tkaTydK4lYhkszmHf2-4jHJEe8Vs,21214
scipy/sparse/linalg/_isolve/minres.py,sha256=lz5MBEKkTIjhiBnWoJ6WhNXGkKiYRKnt2FAI2MNvsmM,11611
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-39.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=M5lrn0JBRUmo6ug2p1SgDtm7PAbU6potiJzRy-wT68Q,5413
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=g2dEqDPRJUuesDn8FrTOQxkZ2wMaOWGao7z7lShV1Ng,25626
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=hAjJLuBtyLMCCqK_uZbTVGnsFACsLZHgtiHdUABRO3Q,7064
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=6bQA3WdneycfXx6aZyFdPjWRUSXm_Smjh9YcJo8R-4E,6365
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=IG6FaJjYU_0QYYCBC4yjNiZldi1ZafIITDKnESTScCo,3754
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=7h3A3dzQV9_jqYrNdulAAJnzZ5icw_HBnTXNXnUdUto,2435
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=VlmvctRaQtjuYvQuoe2t2ufib74Tua_7qsiVrs3j-p0,265
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=SpMqzbNeYBgMU6DYgQyV2SbGlnal6d1iMysAILQj_pI,6689
scipy/sparse/linalg/_isolve/utils.py,sha256=I-Fjco_b83YKUtZPVdobTjPyY41-2SHruVvKZVOIXaU,3598
scipy/sparse/linalg/_matfuncs.py,sha256=wib0cFQFGX9CylfenGMGdDskE5XJ_LTC_OWpLJcfIZY,29385
scipy/sparse/linalg/_norm.py,sha256=y4J98m4JBfHI67lZNsF93SUIiy4JHwhFElFjuZE_twg,6067
scipy/sparse/linalg/_onenormest.py,sha256=47p9H_75GVy3AobAmpgYQQI3Nm7owHVil6ezu42PHsQ,15486
scipy/sparse/linalg/_propack/_cpropack.cpython-39-darwin.so,sha256=iactKmApta8A9f0O4Cdu7v8Ok6DZKlDZYowWiC22eUA,181040
scipy/sparse/linalg/_propack/_dpropack.cpython-39-darwin.so,sha256=vq0PNpeO9VAo8gE8A9Tm6dR_0gaMXYafeSLG2nnhc-g,145872
scipy/sparse/linalg/_propack/_spropack.cpython-39-darwin.so,sha256=sL7XCretiSlJ-xatMFYZpIdhbrUXvHkgyabhMk7Svzo,146064
scipy/sparse/linalg/_propack/_zpropack.cpython-39-darwin.so,sha256=9gD5UkbfJZPADe2ZsIN95mXf484gsl4nhcnt9BuTUlo,164624
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=7jnMobVkXaYQeHODLmaTFwAL-uC-LVda5D1vz-vpz3A,34298
scipy/sparse/linalg/_svdp.py,sha256=3_w6ECB1W0LiFoS400LCtx0NXwKPJETmoF9X1JZ07uI,11415
scipy/sparse/linalg/dsolve.py,sha256=iR9kBE3U5eVFBVJW8bpEGEhFFfR6PiI-NIbqKzLT8U4,697
scipy/sparse/linalg/eigen.py,sha256=SItXs6TCDv9zJFnj8_KyBzJakRC2oeIGDqVEs0sHmzQ,664
scipy/sparse/linalg/interface.py,sha256=JHIM0cIQUEzMmUqhkU69hTy6seeG648_l2XI39nmLvs,682
scipy/sparse/linalg/isolve.py,sha256=BWvUveL2QGKFxqVGDFq2PpGEggkq204uPYs5I83lzgY,671
scipy/sparse/linalg/matfuncs.py,sha256=zwrqI0IwAPhQt6IIJ-oK5W_ixhGMGcYVGcSr2qU6lFI,697
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-39.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_special_sparse_arrays.cpython-39.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=EN5HcjT92SgJuTHX89Ebh-OIgrrR0UVxjcrPYmNAN60,13955
scipy/sparse/linalg/tests/test_interface.py,sha256=MmCzkRdcaIy2DUOYRFRv8px_Hk68AFdepBe8ivbSXLA,17953
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=gPpXsIUZg97wL_fzHodNMyswgZ0h9nqxTqxFu8_3bL0,21885
scipy/sparse/linalg/tests/test_norm.py,sha256=8waDQ-csiw4jTIQPz8qlseqgosvjY9OHfAU7lJ8yLxo,6163
scipy/sparse/linalg/tests/test_onenormest.py,sha256=EYUVD6i7RGiMi_bclm1_4YkLZSAma5CHqRH9YeDvtwM,9227
scipy/sparse/linalg/tests/test_propack.py,sha256=Tvcx6MliY6i_Px0KlKfGwjFCElH5y2Arekm7WVAhKqI,5539
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=KvANTuvxY36U1ctm3SLJp2Kib1oaCe2ZlGEqCxfZ_Fs,6245
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=2Z7r1LPx7QTekuXNTLcspGOdJ9riRwioGIpxzIa0Kh4,12854
scipy/sparse/sparsetools.py,sha256=0d2MTFPJIvMWcTfWTSKIzP7AiVyFGS76plzgzWSXGuQ,2168
scipy/sparse/spfuncs.py,sha256=zcwv-EvwXW-_7kjRJqNm-ZoKbDcxlU4xOuvl3iBWao0,582
scipy/sparse/sputils.py,sha256=coz-V4p4Vg2eT1yc3sZF6_7FXKvj2ZuP7QKhPF4UEb0,973
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_common1d.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_coo.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_deprecations.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_dok.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_minmax1d.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-39.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-39.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_array_api.py,sha256=OWXlJJzLgz9LdbLyJ8PrOaAdDRR8-xJs067jY37AwqI,14465
scipy/sparse/tests/test_base.py,sha256=L0aH2Jy558UmkGZeiEix2e3-og7FEAgUvxqqCC_jq_A,191015
scipy/sparse/tests/test_common1d.py,sha256=ekxz7d4JdPu0khyhDdJ39TzUcuZIMGG24vhMHbtjogI,16141
scipy/sparse/tests/test_construct.py,sha256=6J4zV_rbj-eO7rLiR4kF_3nxf1sf82lzxOzHFif91iM,33414
scipy/sparse/tests/test_coo.py,sha256=opa1NGLbCzMDMIbuxS1nn7kFhFx1cu1WLQTJg8SZe04,8477
scipy/sparse/tests/test_csc.py,sha256=rB2cBXznxPdQbMZpdQyQitUdCdEeO6bWt7tQ_LBGGDw,2958
scipy/sparse/tests/test_csr.py,sha256=efYU3H8Mm3GIB0ZRxXQCZixFo2OB56AR016k-bz33tY,6488
scipy/sparse/tests/test_deprecations.py,sha256=g4bw2bVauWSGt4e0yvDJ1MMkqDtp97kTl77EXwyDsIs,645
scipy/sparse/tests/test_dok.py,sha256=CoB3dxrFhzvuZS4s9jJfTjuNyYv-KwlYkwpESYybAjc,6048
scipy/sparse/tests/test_extract.py,sha256=4qUPrtCv9H7xd-c9Xs51seQCiIlK45n-9ZEVTDuPiv8,1685
scipy/sparse/tests/test_matrix_io.py,sha256=sLyFQeZ8QpiSoTM1A735j-LK4K0MV-L7VnWtNaBJhw4,3305
scipy/sparse/tests/test_minmax1d.py,sha256=HNR0aaPGesVzenx_iXNKTs9bMoGomckk7aeUscjnGx0,2375
scipy/sparse/tests/test_sparsetools.py,sha256=zKeUESux895mYLdhhW_uM5V1c-djdEKnZ-xURx5fNrw,10543
scipy/sparse/tests/test_spfuncs.py,sha256=ECs34sgYYhTBWe4hIkx357obH2lLsnJWkh7TfacjThw,3258
scipy/sparse/tests/test_sputils.py,sha256=h8YJ7QKigGy49OPf_X8KZBF3ZmB5RN3BjghNeMGg3rI,7286
scipy/spatial/__init__.py,sha256=SOzwiLe2DZ3ymTbCiSaYRG81hJfeqSFy5PcccZ3Cwn0,3697
scipy/spatial/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-39.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-39.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-39.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-39.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-39.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-39.pyc,,
scipy/spatial/__pycache__/distance.cpython-39.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-39.pyc,,
scipy/spatial/__pycache__/qhull.cpython-39.pyc,,
scipy/spatial/_ckdtree.cpython-39-darwin.so,sha256=ekw9so8U2c659ldvGYoneeC0U_hxFhzmqJuYALDzbqM,760864
scipy/spatial/_ckdtree.pyi,sha256=rt73FClv4b7Ua0TcIj4gLWWfiNrETMlCFnyqTXzeAQM,5892
scipy/spatial/_distance_pybind.cpython-39-darwin.so,sha256=KD7cEJOPdWLw--0Z9IyaEtN3-1TVvreVqlh9yUTmfdg,550096
scipy/spatial/_distance_wrap.cpython-39-darwin.so,sha256=cVkdgK_q1CfNxIPTM9jFmBBBQVUq1aZ_k2G1VoobLS0,87248
scipy/spatial/_geometric_slerp.py,sha256=WdTteqZuTzrW-ZMXTKehWTplaOJrtqQimAIWWAaW5vM,7981
scipy/spatial/_hausdorff.cpython-39-darwin.so,sha256=UsVGHb1laoyNsrD4_e1SKiHX5vG2NOiWYrIu5kL2JGE,192232
scipy/spatial/_kdtree.py,sha256=9k5hOuUrM7vnVTUp4_IKCJAjaKekCB378inhmYgeBQQ,33443
scipy/spatial/_plotutils.py,sha256=hESt827uWjj14yGCsRCLrpa_oMUMwGJZ0DNRNDPGTfo,7259
scipy/spatial/_procrustes.py,sha256=oj1TnlLsBxlLVXvn7zG5nymeHxQkRMSDzgjsLZGg-9A,4429
scipy/spatial/_qhull.cpython-39-darwin.so,sha256=eDRlRTl6786UUOiR5egqjD3yAlruBttbWiJQVkGEzf0,1038480
scipy/spatial/_qhull.pyi,sha256=dmvze3QcaoA_Be6H8zswajVatOPwtJFIFxoZFE9qR-A,5969
scipy/spatial/_spherical_voronoi.py,sha256=x3TrK6tTkKwfSSSWcdkBOZ9i042t1Hn21oom4aES15U,13539
scipy/spatial/_voronoi.cpython-39-darwin.so,sha256=CyHHAaKpQDqdGmCxQiR2ABtiCvvoewhZVLlezSLilTk,190912
scipy/spatial/_voronoi.pyi,sha256=aAOiF4fvHz18hmuSjieKkRItssD443p2_w1ggXOIs1g,126
scipy/spatial/ckdtree.py,sha256=uvC-phcjhzmGLLcE_tKHPn6zrTTjGwVSren0M4jSPng,645
scipy/spatial/distance.py,sha256=QVH_K3qK3MvElGaoMimK3VNyFmwnuGdq0MvoRumsKRw,91483
scipy/spatial/distance.pyi,sha256=f9eGCqRUYrQt7gI37JnARDn1FkIVsKRlinx2onMshZQ,5273
scipy/spatial/kdtree.py,sha256=Wlqqnd9uwGZ1t7UoL4uIzUhSYo247jaOpokehDGj66o,655
scipy/spatial/qhull.py,sha256=aFE-KscuINt6QIhFC2dqhwFCYu3HSBkVXDH5exHH71s,622
scipy/spatial/qhull_src/COPYING.txt,sha256=NNsMDE-TGGHXIFVcnNei4ijRKQuimvDy7oDEG7IDivs,1635
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-39.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-39.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=ULnYAgX2_AwOVF-VE7XfnW5S0pzhx7UAoocxSnXMaWs,5750
scipy/spatial/tests/data/cdist-X2.txt,sha256=_IJVjXsp3pvd8NNPNTLmVbHOrzl_RiEXz7cb86NfvZ4,11500
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=k19QSfkqhMmByqNMzwWDmM6wf5dt6whdGyfAyUO3AW0,15000
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=5Z9SMsXrtmzeUwJlVmGkrPDC_Km7nVpZIbBl7p3Hdc0,50000
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Yerj1wqIzcdyULlha-q02WBNGyS2Q5o2wAr0XVEkzis,178801
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=NEd2b-DONqUMV9f8gJ2yod17C_5fXGHHZ38PeFsXkyw,3041
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=UCWZJeMkMajbpjeG0FW60b0q-4r1geAyguNY6Chx5bM,178801
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=8Iq7cF8oMJjpqd6qsDt_mKPQK0T8Ldot2P8C5rgbGIU,3041
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=l2kEAu0Pm3OsFJsQtHf9Qdy5jnnoOu1v3MooBISnjP0,178801
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=S4GY3z-rf_BGuHmsnColMvR8KwYDyE9lqEbYT_a3Qag,3041
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=hQzzoZrmw9OXAbqkxC8eTFXtJZrbFzMgcWMLbJlOv7U,178801
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=P92Tm6Ie8xg4jGSP7k7bmFRAP5MfxtVR_KacS73a6PI,3041
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=0Sx5yL8D8pyYDXTIBZAoTiSsRpG_eJz8uD2ttVrklhU,50000
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=3-UwBM7WZa4aCgmW_ZAdRSq8KYMq2gnkIUqU73Z0OLI,178801
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=rkQA2-_d7uByKmw003lFXbXNDjHrUGBplZ8nB_TU5pk,3041
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=IAYroplsdz6n7PZ-vIMIJ4FjG9jC1OSxc3-oVJdSFDM,3041
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=Zb42SoVEnlTj_N_ndnym3_d4RNZWeHm290hTtpp_zO8,3041
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=L7STTmlRX-z-YvksmiAxEe1UoTmDnQ_lnAjZH53Szp0,172738
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=-sZUikGMWskONojs6fJIMX8VEWpviYYg4u1vipY6Bak,2818
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=N5L5CxRT5yf_vq6pFjorJ09Sr-RcnrAlH-_F3kEsyUU,178801
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=DRgzqxRtvQVzFnpFAjNC9TDNgRtk2ZRkWPyAaeOx3q4,3041
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=jz7SGKU8GuJWASH2u428QL9c-G_-8nZvOFSOUlMdCyA,178801
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=37H01o6GibccR_hKIwwbWxGX0Tuxnb-4Qc6rmDxwwUI,178801
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=YmcI7LZ6i-Wg1wjAkLVX7fmxzCj621Pc5itO3PvCm_k,3041
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=IrtJmDQliv4lDZ_UUjkZNso3EZyu7pMACxMB-rvHUj0,3041
scipy/spatial/tests/data/random-bool-data.txt,sha256=MHAQdE4hPVzgu-csVVbm1DNJ80dP7XthJ1kb2In8ImM,6000
scipy/spatial/tests/data/random-double-data.txt,sha256=GA8hYrHsTBeS864GJf0X6JRTvGlbpM8P8sJairmfnBU,75000
scipy/spatial/tests/data/random-int-data.txt,sha256=xTUbCgoT4X8nll3kXu7S9lv-eJzZtwewwm5lFepxkdQ,10266
scipy/spatial/tests/data/random-uint-data.txt,sha256=8IPpXhwglxzinL5PcK-PEqleZRlNKdx3zCVMoDklyrY,8711
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=rkVhIL1mupGuqDrw1a5QFaODzZkdoaLMbGI_DbLLTzM,480
scipy/spatial/tests/test__plotutils.py,sha256=fASbg0i7iLiJIEj5vIkiDuTq3wU0z3mKJY019kzKrFk,3814
scipy/spatial/tests/test__procrustes.py,sha256=wmmnUHRdw_oID0YLi404IEWPH6vEGhvHXSeGPY_idHo,4974
scipy/spatial/tests/test_distance.py,sha256=m0lxDXuZWREXE-k_yMHUddKqnmbRKo-g-VoVEE2Xez0,84153
scipy/spatial/tests/test_hausdorff.py,sha256=n-Qm2gVF0zc11tDSCnXBznt5Mp0E1ekTtzfWXjqG54M,7114
scipy/spatial/tests/test_kdtree.py,sha256=ZlrKMS1JEdkbwFE8WtEMPI3W5H8ldfPjz1D23fcrsKM,49270
scipy/spatial/tests/test_qhull.py,sha256=0U39Wgug1XZaiQw20tMZPQJPQCA0bY8Ttq7fRhe889A,44262
scipy/spatial/tests/test_slerp.py,sha256=hYH-2ROq0iswTsli4c-yBLZfACvQL0QVCKrPWTeBNls,16396
scipy/spatial/tests/test_spherical_voronoi.py,sha256=Ydof8dYsSoYfII5lVDJ82iVynrruwuBdg0_oESw8YoY,14492
scipy/spatial/transform/__init__.py,sha256=vkvtowJUcu-FrMMXjEiyfnG94Cqwl000z5Nwx2F8OX0,700
scipy/spatial/transform/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-39.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-39.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-39.pyc,,
scipy/spatial/transform/_rotation.cpython-39-darwin.so,sha256=O_i1g9AQQ5Zxbohv05Vj750_lCLWVXYkfJEW5VKDFmM,859240
scipy/spatial/transform/_rotation.pyi,sha256=SI2NWoIjma0P-DaicaLVeRtafg8_SUvJeXOry2bVa5A,3080
scipy/spatial/transform/_rotation_groups.py,sha256=XS-9K6xYnnwWywMMYMVznBYc1-0DPhADHQp_FIT3_f8,4422
scipy/spatial/transform/_rotation_spline.py,sha256=M2i8qbPQwQ49D3mNtqll31gsCMqfqBJe8vOxMPRlD5M,14083
scipy/spatial/transform/rotation.py,sha256=eVnQRbOorImPet4qbF0W95z_ptTNR80LSLRT2jBZAc8,612
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-39.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-39.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-39.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=OXUuNE17jyIJGi1vigFL5lCwjbjYEl-g32IEADc5ZwY,62077
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=V6DiLWvJsrdklhS-GlzcA9qEy0cTQpwaNR-7vkhBt1M,5560
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=g3prW5afu_yJxevIz2LMdRFYLfe8zq-3b6TMGw06Ads,5105
scipy/special.pxd,sha256=l9Y21wnx5fZLvrxCeCMUWQvBI5gHx7LBhimDWptxke8,42
scipy/special/__init__.py,sha256=8RBpMhRlS6fAXj1PH0Rj6KkfdTC4E2skg3vZrZ2Q0cs,31975
scipy/special/__pycache__/__init__.cpython-39.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-39.pyc,,
scipy/special/__pycache__/_basic.cpython-39.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-39.pyc,,
scipy/special/__pycache__/_lambertw.cpython-39.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-39.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-39.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-39.pyc,,
scipy/special/__pycache__/_sf_error.cpython-39.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-39.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-39.pyc,,
scipy/special/__pycache__/_support_alternative_backends.cpython-39.pyc,,
scipy/special/__pycache__/_testutils.cpython-39.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-39.pyc,,
scipy/special/__pycache__/basic.cpython-39.pyc,,
scipy/special/__pycache__/orthogonal.cpython-39.pyc,,
scipy/special/__pycache__/sf_error.cpython-39.pyc,,
scipy/special/__pycache__/specfun.cpython-39.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-39.pyc,,
scipy/special/_add_newdocs.py,sha256=cWyckQIFsSlIkK6swKC0OcWx0ZKlLtlC4D-bLVx-6h4,398483
scipy/special/_basic.py,sha256=bLfi_x45vUBVMx89ELLq6skfc7dlTL8jyhkptDm2vrc,103850
scipy/special/_cdflib.cpython-39-darwin.so,sha256=9E5dNBBs2BOiVoTsVcz0_8ivrovK3gvgpD-q1GXXgR4,179720
scipy/special/_comb.cpython-39-darwin.so,sha256=R-gWHGmauOuabc1v379bpvtBJ8j60IUILeYT8iN4SwM,59840
scipy/special/_ellip_harm.py,sha256=YHHFZXMtzdJxyjZXKsy3ocIsV-eg6ne3Up79BuFl9P8,5382
scipy/special/_ellip_harm_2.cpython-39-darwin.so,sha256=SlNObqZpIpub2iSSTCWrYppzDP5RUhJRJSf3atiFJJs,117840
scipy/special/_lambertw.py,sha256=-oSEnHFQWZiUZXMamxPWjfntWq5tt0rzHmI13DxGHBY,3962
scipy/special/_logsumexp.py,sha256=2MyHR5PWo83qt5RrEnXWRCcWS55gy2s5UWDu30LUvaQ,9027
scipy/special/_mptestutils.py,sha256=Yl_tYnFW1j2DbH6I-2MBNjjqt4WiDO-phVWyNj1Hpfw,14441
scipy/special/_orthogonal.py,sha256=jcOgiGPDzhAsxeEmoYhTSDHZ_uSE5TNiG1yTvAliuXI,74558
scipy/special/_orthogonal.pyi,sha256=XATMiU9ri9e39B5YANXPyQkMqWtfu5rDIP4NA7WSQTU,8304
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-39.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-39.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=ZGSeDDpLRsapyx2GbIrqqYR98fvaEQrLn7IE-fuodhE,354
scipy/special/_precompute/expn_asy.py,sha256=JAz0hY1gBJu3Q_dvscQrSJdgKuwpjqFZVwz-sOQQ21w,1265
scipy/special/_precompute/gammainc_asy.py,sha256=P5OFRcPkkpjGQeYCaMZ8SFSUmZG_CjrEHv8OLwgcGFc,2502
scipy/special/_precompute/gammainc_data.py,sha256=Y5taFAdCE3W14bavUACTA3XoCxyh7_Z2NHcs-DKS75E,4077
scipy/special/_precompute/lambertw.py,sha256=7f4F3ivouVNZwuvVX8TAi2lPB7LirPS8IfN5lEw9zI0,1961
scipy/special/_precompute/loggamma.py,sha256=iq7ZBrUmk8pXYZwO_wINI4u8ENsLbL9VUShGjGO0Pt0,1094
scipy/special/_precompute/struve_convergence.py,sha256=z7R0Q5_Ye-EqLI9g-yARdl_j5FooofXMRXPLVrIFJQQ,3624
scipy/special/_precompute/utils.py,sha256=JXJuI07Jlm4bDHJFVtj0jHq05p-V1ofeXZB16Y05kzI,887
scipy/special/_precompute/wright_bessel.py,sha256=7z2W3spGANZO31r_xauMA6hIQ0eseRlXx-zJW6du5tU,12868
scipy/special/_precompute/wright_bessel_data.py,sha256=f1id2Gk5TPyUmSt-Evhoq2_hfRgLUU7Qu_mELKtaXGg,5647
scipy/special/_precompute/wrightomega.py,sha256=YpmLwtGJ4qazMDY0RXjhnQiuRAISI-Pr9MwKc7pZlhc,955
scipy/special/_precompute/zetac.py,sha256=LmhJP7JFg7XktHvfm-DgzuiWZFtVdpvYzzLOB1ePG1Q,591
scipy/special/_sf_error.py,sha256=q_Rbfkws1ttgTQKYLt6zFTdY6DFX2HajJe_lXiNWC0c,375
scipy/special/_specfun.cpython-39-darwin.so,sha256=UVf2enEVhjfwxJebkm4tg6UfKOLtvxOP3fYX_i68g-w,237912
scipy/special/_spfun_stats.py,sha256=IjK325nhaTa7koQyvlVaeCo01TN9QWRpK6mDzkuuAq0,3779
scipy/special/_spherical_bessel.py,sha256=XbbMLs_0qsmbuM7hIb0v6LPn5QrKLwhwAQYl5PtZYjc,10420
scipy/special/_support_alternative_backends.py,sha256=SYomM7-qPmsMO_0UYzfpVAAdaU9Y9gPb6F6g0xBOnOo,2294
scipy/special/_test_internal.cpython-39-darwin.so,sha256=VY3mcywhszWvsq6DT-fI5vy8FVqT8YuawppjZQmgOIc,210616
scipy/special/_test_internal.pyi,sha256=BI0xSfTmREV92CPzaHbBo6LikARpqb9hubAQgTT0W6w,338
scipy/special/_testutils.py,sha256=pnEE50AZrNe2FJ92fM1rsEcTY7lR-zYBE2paEPhI-wk,12027
scipy/special/_ufuncs.cpython-39-darwin.so,sha256=qrkiucFPPpSFESUbGS84801SIj1OMbgG63x_eHq4i_0,1235680
scipy/special/_ufuncs.pyi,sha256=Bop_e3jGG-wWIrCehOwR7Aa_qEuk-TfWi0C2Phkknmc,8937
scipy/special/_ufuncs.pyx,sha256=yM5T3uRffyZS1vCfdBke-Kpdd9Y8GE0a0Ozpifl-EDw,890803
scipy/special/_ufuncs_cxx.cpython-39-darwin.so,sha256=k7nRYwuEorrueD_K5UWvgQz4Fk8-sT1TzMoTaiCwfhU,518752
scipy/special/_ufuncs_cxx.pxd,sha256=xBBTzhemAneLScqm5Tf3Ufz64gfrMVoeKfE5-EpZmXM,1951
scipy/special/_ufuncs_cxx.pyx,sha256=uwWM8H7h3Os4NvGdN6fE8OmWi5rN_rZZlnBN15eTvIU,10940
scipy/special/_ufuncs_cxx_defs.h,sha256=Qi71Kwn1-fg0klmk8fBuGq0x7-DoolwkoJzaH4gyc34,2972
scipy/special/_ufuncs_defs.h,sha256=Yhew1gtfnDeBLn6aQr0ysVmJwehm2R_4PqxlJAFAl7E,9216
scipy/special/add_newdocs.py,sha256=np1hD4g1B2jNT4SOMq-6PUkTsGMBEucT5IuL3kcflCg,469
scipy/special/basic.py,sha256=LRU8rIxXx42O4eVZv21nFwswAu7JFtQ42_4xT5BwYpE,1582
scipy/special/cython_special.cpython-39-darwin.so,sha256=kRAH321zIgqB-RcV9jzsxBso6bIGKsQLFX5zr44wT7I,2278744
scipy/special/cython_special.pxd,sha256=OzvZ0di3svc0wvTDEkufTwHCDiDU-F1GygJvsy_Kq0o,16349
scipy/special/cython_special.pyi,sha256=BQVUCzV8lCylnmLCtnN0Yz_ttlqyzcLc-BZx2KPXPzM,58
scipy/special/cython_special.pyx,sha256=E7lNHH4Jq07mM3keMhgxLmXn6i-qoTh421Ur1OSy2SY,142731
scipy/special/orthogonal.py,sha256=2uWRTD_Wg83YzaMwYY8BAdyGVy4Z3iEc7ne5rLpdudo,1830
scipy/special/sf_error.py,sha256=wOZqzX7iipkH39hOHqBlkmretJRbYy-K7PsnZPyaJFU,573
scipy/special/specfun.py,sha256=bChigh8GnoirH0wQ8j_D_AY77Pl0Pd8ZqGNgjIMAZ84,826
scipy/special/special/binom.h,sha256=Nbs4PzhKl-3bSs9AaslHgYYkQy3rHtb8ZiTXqqicW80,2359
scipy/special/special/cephes/beta.h,sha256=V9TjdBG6gRBVykHA3fNL0fQZAdnIWxd2RbEkZ5bQkNA,7012
scipy/special/special/cephes/const.h,sha256=ITr0sKUAP4CcYicPmmk65M9XFVupRgfF3FiqOewlbAI,2599
scipy/special/special/cephes/gamma.h,sha256=AsGJQL5c7V9gahXe3B5_dFIfOsEK2KWqK4X8ECY3EHU,10337
scipy/special/special/cephes/polevl.h,sha256=ClCCS13O-ePqXSxvmsPZNZR_RoyZQW7xMQo0ePSQmDU,4025
scipy/special/special/cephes/psi.h,sha256=O9ZDjk-CbhsTpbg9jfQI5VxnxJYu9h5KfGUlf2mISxQ,6323
scipy/special/special/cephes/trig.h,sha256=NvkMCTA1TpscUcqSQ1EIlbs7FYST2SyUdXvG2_EvANE,1304
scipy/special/special/cephes/zeta.h,sha256=IvdUT0PdHreDUsPpjqiY4Uhvz0kq6tyegbY2CwU2u4w,4386
scipy/special/special/config.h,sha256=aMf_pNKWE1iAgJNSnaCKqdPNuKK3Zq9uuck8h6f8Ggs,4315
scipy/special/special/digamma.h,sha256=TG6_ayajnm-RQByvYF1ohZ93TxwDdnJwaAWoiRGDCRU,7303
scipy/special/special/error.h,sha256=_sd-2bgRyCtPMb4wLD57i8GmfuYOINeP_o40iRRwvgE,1191
scipy/special/special/evalpoly.h,sha256=E_GM-Idr-dF5WfeRdvhiYCioNtKRZ10kTBMON8wWm08,1131
scipy/special/special/lambertw.h,sha256=E59hB9vFOQ3cr_jMrbt9xmwJTkXxTY4FGIFBJh-DSms,5205
scipy/special/special/loggamma.h,sha256=eQFXyU7sOsRySn7GWV2DypOSfrwfEngSgZ3gTFKuC8k,6000
scipy/special/special/trig.h,sha256=fLojwOOecF_eRJU5H86THXbZq1dK1hjVG98cLzN4WSg,3116
scipy/special/special/zlog1.h,sha256=uojL5H_Oe7CipENnvenHNjUkDcXXK0qe6ynocDwSYuQ,977
scipy/special/spfun_stats.py,sha256=fYFGN-9Q3X9zdm9KTyW6t2oixuaZzQwd_h0eyVvfGBk,545
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_specfun.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_support_alternative_backends.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-39.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-39.pyc,,
scipy/special/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/__pycache__/__init__.cpython-39.pyc,,
scipy/special/tests/data/boost.npz,sha256=1z7Lu1FlRSI0K6BHCmJjqWhOYXwrg3RWX-OnlZP0sjE,1270643
scipy/special/tests/data/gsl.npz,sha256=rKtwAgjLswHuUesfUSyxwn57TnUz_FpfXNXF1qoZfdg,51433
scipy/special/tests/data/local.npz,sha256=ECuHbCfsTS-AQdWrL7bf78gUcCEzUWD1FUVeU-Bocf8,203438
scipy/special/tests/test_basic.py,sha256=BTZJcXsbVSM1p9nw6H1no_SW7hN7NjjcrPnkVQ4EHiQ,171748
scipy/special/tests/test_bdtr.py,sha256=QwGyt0tnutuou25mS0u2LjRgDTYI6ohM2cbZ-He6Os4,3231
scipy/special/tests/test_boxcox.py,sha256=gUrGF7Ql1adxiPl_YxpsGunDfg-B_WpqI9Zghzool7o,2672
scipy/special/tests/test_cdflib.py,sha256=zWmnQvdBdSbrlHg_kzoYBs5wfsVXiDuVH1N_2B5Ro48,17441
scipy/special/tests/test_cdft_asymptotic.py,sha256=DBVVLaduZUHSWlKJ5aBXmxgdNm_YjLvWgyiTTcQq04c,1441
scipy/special/tests/test_cosine_distr.py,sha256=zL7aWLisIEy1oNKjcynqncgsCxcPKvPb9Odr-J5Xa1M,2690
scipy/special/tests/test_cython_special.py,sha256=3uVOa0p0OdaqxBWeyewQuedpnQtxDJB5kYolf1vRjoA,18838
scipy/special/tests/test_data.py,sha256=iXTMMdNj-jCaXSVbhw3KTQrzLSk5wNQEdRBEDZ_2Cug,30269
scipy/special/tests/test_dd.py,sha256=GROHQEkzIAW6KXkj8J3nPcRDAONcf1nCoArcfx30_5s,1974
scipy/special/tests/test_digamma.py,sha256=Bm7Hh_aETx6MTN3Wu7Sijy4rYGR_1haNGsi3xfzrAKM,1382
scipy/special/tests/test_ellip_harm.py,sha256=51KiCpQjqmf2uLZEsty-Vmr0FhoABtvMUz4218WR_S0,9640
scipy/special/tests/test_erfinv.py,sha256=fzdEHd6MxfSyzQDO93qndXukG2jWj-XNY2X4BJRIdBI,3059
scipy/special/tests/test_exponential_integrals.py,sha256=hlzNhZEXjo5ioPteG0P85qXuMmVD-WVc67e049tvY8Q,3687
scipy/special/tests/test_faddeeva.py,sha256=YLY3Ylp4u_8zxTGxOb5kxNfXXEW0ld_GP2ceOR2ev_Y,2568
scipy/special/tests/test_gamma.py,sha256=hb-ZlA2ZNz6gUGvVtMBgXFl_w30HPmthuUEAmNcz0sw,258
scipy/special/tests/test_gammainc.py,sha256=Avv52EDQ7M8kUpiVU1BVsW_Gj5HDCzAOojLtoFojKbw,3815
scipy/special/tests/test_hyp2f1.py,sha256=knYs5n6I8DwQEfbEj-CtXin9xPepe71Doqx1vQ3FYb0,78549
scipy/special/tests/test_hypergeometric.py,sha256=LqbHLHkdsw8RnVeClpulG6rHRykqZsAyP43AUsKSiQI,5596
scipy/special/tests/test_kolmogorov.py,sha256=0UoQN7q_De8Mx1NEUzhl9KGLNT8fdq6QoX11_vNS3e4,19410
scipy/special/tests/test_lambertw.py,sha256=vd5G_70CQz3N_U15mcyE0-2KZ_8QYLKmrJ4ZL-RwFXY,4560
scipy/special/tests/test_log_softmax.py,sha256=JdiC5C1Fm16rNdQHVWRu-FGMVOv24DPWRnguDDd1zEY,3415
scipy/special/tests/test_loggamma.py,sha256=x6kuJf-bEnn5ECdkDSgvk3An_A-9UxVsZpqa49IwAq8,1992
scipy/special/tests/test_logit.py,sha256=PvIgcK33vQjcvHE3_3fVarKTjZ0t35-ksZnhvoqKQrA,5540
scipy/special/tests/test_logsumexp.py,sha256=Y4hPV6_KotWabV-v2OYVzz_tweKRlHXPCRVFqFk_0fY,6545
scipy/special/tests/test_mpmath.py,sha256=h0rtQEkOubS2J_2DPq55pVn7dQmrDsiF6kemEWPSwNk,72665
scipy/special/tests/test_nan_inputs.py,sha256=8aIQJ2Xz1O4Lr7cJz9KDjFj5SEVjccu3j8auelQ3lj8,1831
scipy/special/tests/test_ndtr.py,sha256=-UMxTIi4CaaLoJ5-SGW9THChPIM3e1_fTY0L877ioNA,2680
scipy/special/tests/test_ndtri_exp.py,sha256=13eabgdbfcL37RReiUH7g9amT9XMsTLOfwxFJXR_2Ww,3708
scipy/special/tests/test_orthogonal.py,sha256=lPVOwR_LSrShHfCkhTrRMc2yJj0q3d6f54cW3-cwsVY,31538
scipy/special/tests/test_orthogonal_eval.py,sha256=iT9QWDaz-V0J77mavxktZ-2oBdJ8y2JifOqiO-wGxk8,9491
scipy/special/tests/test_owens_t.py,sha256=zRbiKje7KrYJ25f1ZuIBfiFSyNtK_bnkIW7dRETIqME,1792
scipy/special/tests/test_pcf.py,sha256=RNjEWZGFS99DOGZkkPJ8HNqLULko8UkX0nEWFYX26NE,664
scipy/special/tests/test_pdtr.py,sha256=VmupC2ezUR3p5tgZx0rqXEHAtzsikBW2YgaIxuGwO5A,1284
scipy/special/tests/test_powm1.py,sha256=9hZeiQVKqV63J5oguYXv_vqolpnJX2XRO1JN0ouLWAM,2276
scipy/special/tests/test_precompute_expn_asy.py,sha256=bCQikPkWbxVUeimvo79ToVPgwaudzxGC7Av-hPBgIU4,583
scipy/special/tests/test_precompute_gammainc.py,sha256=6XSz0LTbFRT-k0SlnPhYtpzrlxKHaL_CZbPyDhhfT5E,4459
scipy/special/tests/test_precompute_utils.py,sha256=MOvdbLbzjN5Z1JQQgtIyjwjuIMPX4s2bTc_kxaX67wc,1165
scipy/special/tests/test_round.py,sha256=oZdjvm0Fxhv6o09IFOi8UUuLb3msbq00UdD8P_2Jwaw,421
scipy/special/tests/test_sf_error.py,sha256=iXZ3bCSQ3oa5_PvrJSfpZme4Ymix5drIcE1Ji2Kfwqo,3902
scipy/special/tests/test_sici.py,sha256=w4anBf8fiq2fmkwMSz3MX0uy35NLXVqfuW3Fwt2Nqek,1227
scipy/special/tests/test_specfun.py,sha256=4nKU8JoGF8s4hHo0m_mUZpScU4ZkYKVhVLTBcjxVouc,1196
scipy/special/tests/test_spence.py,sha256=fChPw7xncNCTPMUGb0C8BC-lDKHWoEXSz8Rb4Wv8vNo,1099
scipy/special/tests/test_spfun_stats.py,sha256=mKJZ2-kLmVK3ZqX3UlDi9Mx4bRQZ9YoXQW2fxrW2kZs,1997
scipy/special/tests/test_sph_harm.py,sha256=ySUesSgZBb4RN-QES2L6G6k3QGOCdGLt86fjJ-6EYiQ,1106
scipy/special/tests/test_spherical_bessel.py,sha256=KaBad7gtKhW_rftAS2kFnwmhetxONFBj3sKlxVDI8T0,14378
scipy/special/tests/test_support_alternative_backends.py,sha256=PHpXGaxGDvJeZS6mcGTxTHHDf1b2HnWh_dX1i0oLKpU,2650
scipy/special/tests/test_trig.py,sha256=ZlzoL1qKvw2ZCbIYTNYm6QkeKqYUSeE7kUghELXZwzU,2332
scipy/special/tests/test_wright_bessel.py,sha256=v1yLL6Ki01VuKPj5nfL-9_FaACvwdIlDsarKsm-z9EQ,4155
scipy/special/tests/test_wrightomega.py,sha256=BW8TS_CuDjR7exA4l6ADnKhXwgFWUYaN1UIopMBJUZY,3560
scipy/special/tests/test_zeta.py,sha256=IoBUdssBRj7noPjW-xs9xGFFihZ7wvQpPJidgMOFCOs,1367
scipy/stats/__init__.py,sha256=k9cOA7sGZ_GO0_AbE9ecVlg-zsq2vbM6HBjKh4CjHjM,18163
scipy/stats/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-39.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-39.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-39.pyc,,
scipy/stats/__pycache__/_bws_test.cpython-39.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-39.pyc,,
scipy/stats/__pycache__/_common.cpython-39.pyc,,
scipy/stats/__pycache__/_constants.cpython-39.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-39.pyc,,
scipy/stats/__pycache__/_covariance.cpython-39.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-39.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-39.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-39.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-39.pyc,,
scipy/stats/__pycache__/_entropy.cpython-39.pyc,,
scipy/stats/__pycache__/_fit.cpython-39.pyc,,
scipy/stats/__pycache__/_generate_pyx.cpython-39.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-39.pyc,,
scipy/stats/__pycache__/_kde.cpython-39.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-39.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-39.pyc,,
scipy/stats/__pycache__/_morestats.cpython-39.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-39.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-39.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-39.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-39.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-39.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-39.pyc,,
scipy/stats/__pycache__/_qmc.cpython-39.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-39.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-39.pyc,,
scipy/stats/__pycache__/_resampling.cpython-39.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-39.pyc,,
scipy/stats/__pycache__/_rvs_sampling.cpython-39.pyc,,
scipy/stats/__pycache__/_sampling.cpython-39.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-39.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-39.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-39.pyc,,
scipy/stats/__pycache__/_survival.cpython-39.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-39.pyc,,
scipy/stats/__pycache__/_variation.cpython-39.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-39.pyc,,
scipy/stats/__pycache__/_wilcoxon.cpython-39.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-39.pyc,,
scipy/stats/__pycache__/contingency.cpython-39.pyc,,
scipy/stats/__pycache__/distributions.cpython-39.pyc,,
scipy/stats/__pycache__/kde.cpython-39.pyc,,
scipy/stats/__pycache__/morestats.cpython-39.pyc,,
scipy/stats/__pycache__/mstats.cpython-39.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-39.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-39.pyc,,
scipy/stats/__pycache__/mvn.cpython-39.pyc,,
scipy/stats/__pycache__/qmc.cpython-39.pyc,,
scipy/stats/__pycache__/sampling.cpython-39.pyc,,
scipy/stats/__pycache__/stats.cpython-39.pyc,,
scipy/stats/_ansari_swilk_statistics.cpython-39-darwin.so,sha256=WCnO1zl9LM4Iga9AHiUieMUE4t4K1LfGFOBTGeN03BI,227224
scipy/stats/_axis_nan_policy.py,sha256=NnZZH10vl4E8UNNosfmMWh-lv8Xr_4LWeuuwQhJw1qI,29107
scipy/stats/_biasedurn.cpython-39-darwin.so,sha256=UsAvhfsK-UVrWTsOO0utLvt2ZLIC6Y1g8BLg7mdzttQ,252152
scipy/stats/_biasedurn.pxd,sha256=bQC6xG4RH1E5h2jCKXRMADfgGctiO5TgNlJegKrR7DY,1046
scipy/stats/_binned_statistic.py,sha256=JYbpISuP2vn7U0FD7W5CWffC2dbMwAVeBLIlKJyxy8Q,32712
scipy/stats/_binomtest.py,sha256=aW6p-vRkv3pSB8_0nTfT3kNAhV8Ip44A39EEPyl9Wlc,13118
scipy/stats/_boost/__init__.py,sha256=e1_a5N-BBpz7qb0VeLQ7FOEURW9OfQ3tV42_fMDVkOU,1759
scipy/stats/_boost/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/_boost/beta_ufunc.cpython-39-darwin.so,sha256=gx70Gtq3tDAHpS3eYf0dqoOXdQXe8_YSNZNx0rqlYms,206160
scipy/stats/_boost/binom_ufunc.cpython-39-darwin.so,sha256=BUswrvOKByZO_vjkz87nsTXaJXSVjBRQaeKflWjgTW8,169576
scipy/stats/_boost/hypergeom_ufunc.cpython-39-darwin.so,sha256=SFOzcGckMKZhkpzhCK43rcuReVgmy7p72sxczQfic0Y,125320
scipy/stats/_boost/invgauss_ufunc.cpython-39-darwin.so,sha256=KePoOvpZLGXhzNYA2andFKfe4hqD5GYXr6PBtVLi7GI,173968
scipy/stats/_boost/nbinom_ufunc.cpython-39-darwin.so,sha256=uKUopHB7cKYZdJzrffKnt1fyuVHZMBn8m4JKAGa5sLM,189008
scipy/stats/_boost/ncf_ufunc.cpython-39-darwin.so,sha256=OceU_U1Ice4ZFtiaoE6eA5nwbkq2EIuOSwoRadCNn5A,167976
scipy/stats/_boost/nct_ufunc.cpython-39-darwin.so,sha256=gURGgrqqLHDal2ev6dg6ergZnejMJ_yirVB7XSeR-s0,227856
scipy/stats/_boost/ncx2_ufunc.cpython-39-darwin.so,sha256=O4ckX6TIC04grOSLFi-QfBpGEWRkPt6pUmDacUxUR58,176000
scipy/stats/_boost/skewnorm_ufunc.cpython-39-darwin.so,sha256=dl1oOCsa-tvl4SQ9XNqxLqAGdv9C36x2GFtbmtja5BI,103624
scipy/stats/_bws_test.py,sha256=XQMGiLMPKFN3b6O4nD5tkZdcI8D8vggSx8B7XLJ5EGs,7062
scipy/stats/_censored_data.py,sha256=Ts7GSYYti2z-8yoOJTedj6aCLnGhugLlDRdxZc4rPxs,18306
scipy/stats/_common.py,sha256=4RqXT04Knp1CoOJuSBV6Uy_XmcmtVr0bImAbSk_VHlQ,172
scipy/stats/_constants.py,sha256=_afhD206qrU0xVct9aXqc_ly_RFDbDdr0gul9Nz6LCg,962
scipy/stats/_continuous_distns.py,sha256=RZ-FjHC7rjOVOUTtcTeEqv5RukCHmmPsgaegVT5Wtgw,386201
scipy/stats/_covariance.py,sha256=vu5OY1tuC5asr3FnwukQKwwJKUDP-Rlp0Kbe1mT36qM,22527
scipy/stats/_crosstab.py,sha256=f4Sqooh-gPyTjLMHRbmhkVaOT-nhrOZ2NJ-gfPjvyuY,7355
scipy/stats/_discrete_distns.py,sha256=VJMbi04QAdug7zsk7t2o3N6zWzx_XH3yCefS83KYPWc,59419
scipy/stats/_distn_infrastructure.py,sha256=3QkGaXLtQF-AF4KhHamPCJSJQVXekOQmkX2tNpWUTv4,148306
scipy/stats/_distr_params.py,sha256=I7kGoCDJm0e8XsoRpMe5zz7KY9OHErNdW529npPkC2g,8732
scipy/stats/_entropy.py,sha256=b0wlhLQRWEIDZrOTMFfRwx4aPE6HqnJ6HTtBGoGXrpM,15232
scipy/stats/_fit.py,sha256=_Abj6CcENqRz0z4O27Zp1q002JrXzdnKCo2KL7RjvUg,59771
scipy/stats/_generate_pyx.py,sha256=gHEsVa0zFLC5CSEpsalRLxA0R6DP1ghV9VPV1_ZxDh8,829
scipy/stats/_hypotests.py,sha256=-3f22z3TZNK7W_Cu-xmf2vy_gALLXYW3paYw48sNzcI,78852
scipy/stats/_kde.py,sha256=8eZxz9JkZXUphFb6-ibzvT2fUpMY615kU4KmwRYMu4I,25138
scipy/stats/_ksstats.py,sha256=Svh0qUd7GI1qmMNRIlv8_AfH0Rf7SmVn9mQ2gQdjd3k,20116
scipy/stats/_levy_stable/__init__.py,sha256=n6IgB_ZpXpe05d3399bs31shsCZVepUOIrrW7pt149g,45541
scipy/stats/_levy_stable/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/_levy_stable/levyst.cpython-39-darwin.so,sha256=e9HryM0IXIobbxoS_5kPb-PBK9-IYWJ1V7GsqFKsSsA,61920
scipy/stats/_mannwhitneyu.py,sha256=GojWBxRMWgQEGGSJjona90xX18AYiKcSPjJy9rvqtV0,20522
scipy/stats/_morestats.py,sha256=RwInwxrEuX7q4GORyyVN6AVnXPVLCaoO2t-RZS3dK_k,186567
scipy/stats/_mstats_basic.py,sha256=2mJYZK1eNIgRcptmSjZgKsRr0DKtNCAbxLEQiwuvRWA,119363
scipy/stats/_mstats_extras.py,sha256=TeBf3hF0OtcnDk3pTW6iutrzW0H0T7dXx923gHib2pY,16370
scipy/stats/_multicomp.py,sha256=ae_nYfCQVLduyPb5sRTCcV0MpcymnV4H8SM35u3E8NY,17282
scipy/stats/_multivariate.py,sha256=ZPrMbYAus8PUyWDWu87ZWf7fdhQUQrqsX8okqlnQmFY,237847
scipy/stats/_mvn.cpython-39-darwin.so,sha256=VhWKta9wFLHxwBg_7_eSMUY2JJNMnMIEEPMQrgPmkZA,90472
scipy/stats/_odds_ratio.py,sha256=S_zkibLVH7K8Qj6IO6sTkXtq-lGsp8sj_wIXitgu7Es,17858
scipy/stats/_page_trend_test.py,sha256=OvisWd3E6CF7rdFRGv46HWOfJlyHalMITt5iJPzE8LI,18987
scipy/stats/_qmc.py,sha256=ZwXM8sAjx8NfkHXQOC6uEdvIydj-vSfHVks73njFGnY,99365
scipy/stats/_qmc_cy.cpython-39-darwin.so,sha256=kLR1H74yXT3iJlQAk4Ib_32YlADJ--WvfcFShaq87gA,236496
scipy/stats/_qmc_cy.pyi,sha256=xOpTSlaG_1YDZhkJjQQtukbcgOTAR9FpcRMkU5g9mXc,1134
scipy/stats/_qmvnt.py,sha256=Mss1xkmWwM3o4Y_Mw78JI-eB4pZBeig47oAVpBcrMMc,18767
scipy/stats/_rcont/__init__.py,sha256=dUzWdRuJNAxnGYVFjDqUB8DMYti3by1WziKEfBDOlB4,84
scipy/stats/_rcont/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/_rcont/rcont.cpython-39-darwin.so,sha256=n9xZxfBXHYWS7lmlrxpJUE6XTRAL-WUyjCxbPtwVKIU,246312
scipy/stats/_relative_risk.py,sha256=5zeYBMshYwtomiLTkaXc1nmWYD0FsaQNjf0iuDadtSc,9571
scipy/stats/_resampling.py,sha256=4PzopnEwUUZVMkPZlcBl4fddOu1HCZolna8iOmPenXc,81473
scipy/stats/_result_classes.py,sha256=_ghuGdpFsCMuEmnfHg1AeorR-fASc77ACXYWEmQzXjI,1085
scipy/stats/_rvs_sampling.py,sha256=Hz5U8lTHrVPZtGg-OeAKzSA5HW9M51OwH8AU4j2xXVM,2233
scipy/stats/_sampling.py,sha256=YJ1mG2tkXW4Em-virElY-cNzMXn8lHbOxNxujqDsPY0,46408
scipy/stats/_sensitivity_analysis.py,sha256=qu5mNpZZhggy0mywqB8jsqcZZagzsH0mICG4FIz7bhM,24745
scipy/stats/_sobol.cpython-39-darwin.so,sha256=Bbk-MsevlFkGnjxFOwrdSja_OGem_NTYtvgXO3YCae8,332448
scipy/stats/_sobol.pyi,sha256=TAywylI75AF9th9QZY8TYfHvIQ1cyM5QZi7eBOAkrbg,971
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_stats.cpython-39-darwin.so,sha256=ke4gHXO5h_2XuBIUqk8HPEx-6Ln4kTUVqZ2I8n6NpAY,647456
scipy/stats/_stats.pxd,sha256=US2p3SKahv_OPhZClWl_h3cZe7UncGZoQJeixoeFOPg,708
scipy/stats/_stats_mstats_common.py,sha256=ken8kD9hSgUOhmN6biu0d9QNaumzMB5uLb04ZQeib0Y,18593
scipy/stats/_stats_py.py,sha256=7Ny49fBYXJkDUB4q55MuTm1z4ZPjbZTjZvcbtUtIqnQ,423593
scipy/stats/_stats_pythran.cpython-39-darwin.so,sha256=byDCbvcUx7RD0M9YiUa_2NbZVXx9Gj-HuhiX5YHjd04,167808
scipy/stats/_survival.py,sha256=a6pNTOpNnkq3XFoGuid1cJrsObuzpgI7psUzP0PU2j0,26005
scipy/stats/_tukeylambda_stats.py,sha256=eodvo09rCVfcYa1Uh6BKHKvXyY8K5Zg2uGQX1phQ6Ew,6871
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/_unuran/unuran_wrapper.cpython-39-darwin.so,sha256=67ba41yL95EnUKZXNush28OGs2N_Sj_I31d48QVdurc,1328840
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=RGAWLNAHrkAtaS-EjIkcTIr7sag9b0Lx_3i7s_keBfk,5551
scipy/stats/_variation.py,sha256=oHqUpfaL49IxpLmgac1te5Av5MXuScP9XrxRzywJR6I,4375
scipy/stats/_warnings_errors.py,sha256=MpucxNFYEDytXh7vrZCMqTkRfuXTvvMpQ2W_Ak2OnPk,1196
scipy/stats/_wilcoxon.py,sha256=2hF8RL2zqqVWSFLJdds2MnS4MiGKN-3zPbkb0iXqY4M,7999
scipy/stats/biasedurn.py,sha256=kSspd2wFUf85L3FgTYA04jg7oq9ROtqppSMMoPfPm7E,529
scipy/stats/contingency.py,sha256=8Imh2sKSk_il8o55LaQTC0HMODNnjC4aAv4RW6W0zCk,16275
scipy/stats/distributions.py,sha256=9Kt2fyTohorJcf6a7M9DYH8Nu4jEU66nKP01cRhKmuE,859
scipy/stats/kde.py,sha256=_Bawa8xgGYr6hM1c7AM1eKFSZMuV124sA_NIKUqG7Ho,720
scipy/stats/morestats.py,sha256=q2zUyJucrLoBeADOzPjI8ZeOXvuAzg_wGowBG4EdmMU,1391
scipy/stats/mstats.py,sha256=aRbrykjrvl-qOBkmGjlFMH4rbWYSqBBQHReanSAomFg,2466
scipy/stats/mstats_basic.py,sha256=y0qYsc9UjIN6FLUTDGRZSteuDvLsvyDYbru25xfWCKQ,1888
scipy/stats/mstats_extras.py,sha256=aORMhUJUmlI23msX7BA-GwTH3TeUZg1qRA9IE5X5WWM,785
scipy/stats/mvn.py,sha256=1vEs5P-H69S2KnQjUiAvA5E3VxyiAOutYPr2npkQ2LE,565
scipy/stats/qmc.py,sha256=qN3l4emoGfQKZMOAnFgoQaKh2bJGaBzgCGwW1Ba9mU4,11663
scipy/stats/sampling.py,sha256=Tyd68aXwZV51Fwr5pl41WapJ05OG3XWWcYlsQeg6LgA,1683
scipy/stats/stats.py,sha256=YPMYFQOjf3NFWt1kkXTZNMe62TpHaaBDa7CjIvQkw24,2140
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-39.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_boost_ufuncs.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_fast_gen_inversion.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-39.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-39.pyc,,
scipy/stats/tests/common_tests.py,sha256=buhvK6hFtUkMIu1iKuiqXwbg_IGeVJ0e4Ml66xuzFXg,12288
scipy/stats/tests/data/__pycache__/_mvt.cpython-39.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-39.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=OvFCmMqI74DWIgo32UV55dP1nzvFvYBSyYcmKJes9pI,6905
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=BKxPAi4h3IOebcZYGxCbutYuAX0tlb40P0DEkfEi918,27349
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=Qdd0i7H4cNhAABfFOZPuplhi_9SCquFpO-hNkyRcMD8,3063
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=x9wJ2g1qnzf4DK_w9F_WiOiDMDEg4td2z6uU77G07xM,1947
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=KdnJedRthF7XLA-w7XkIPIMTgzu89yBAMmZA2H4uQOQ,6055
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=nCPyxRk1dAoSPWiC7kG4dLaXs2GL3-KRXRt2NwgXoIA,46561
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=6yPHiQSk0KI4oURQOk99t-uEm-IZN-8eIPHb_y0mQ1U,451566
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=fI-HpgJF9cdGdBinclhVzOcWCCc5ZJZuXalUwirV-lc,6815
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=iJTaAWUFn7DPLTd9bQh_EMKEK1DPG0fnN8xk7BQlPRE,53799
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=riOkYT-LRgmJhPpCK32x7xYnD38gwnh_Eo1X8OK3eN8,523605
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=QtSS11d-vkVvqaIEeJ6oNwyET1CKoyQqjlfBl2sTOJA,7381
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=qrxQQ0I6gnhrefygKwT48x-bz-8laD8Vpn7c81nITRg,59228
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=qmELOQyNlH7CWOMt8PQ0Z_yxgg9Hxc4lqZOuHZxxWuc,577633
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=zD_RTRxfqJHVZTAAyddzLDDbhCzKSfwFGr3hwZ1nq30,2591
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=icZGNBodwmJNzOyEki9MreI2lS6nQJNWfnVJiHRNRNM,29239
scipy/stats/tests/test_axis_nan_policy.py,sha256=pNw12PLiF58FVRUPvFvE-DbNGokYS8AH-QFcyJO-lV0,51478
scipy/stats/tests/test_binned_statistic.py,sha256=WE5KdJq4zJxZ1LuYp8lv-RMcTEyjuSkjvFHWsGMujkM,18814
scipy/stats/tests/test_boost_ufuncs.py,sha256=B9lwHkVasspQA78Rz3vtLQESnPRC7Z6R9druZeebs9Q,1825
scipy/stats/tests/test_censored_data.py,sha256=pAQfSHhmcetcxoS1ZgIHVm1pEbapW7az7I-y_8phb5w,6935
scipy/stats/tests/test_contingency.py,sha256=fMeGnTldQjLa5CSaaQ6qH90JXzrUivthVD-9DafgQm0,7706
scipy/stats/tests/test_continuous_basic.py,sha256=-XYuKdMujql8lSh3Xq-vX0UGV32RI0-S0722lmepnkg,41793
scipy/stats/tests/test_continuous_fit_censored.py,sha256=7hu1sSo9hhh0g9pmPMmjj2BI2rkxvA1h20XdMYZeyog,24188
scipy/stats/tests/test_crosstab.py,sha256=tvCoZGfVasNIhYxLQIe3dcdMm34s2ykxxPmCRTIOFc0,3882
scipy/stats/tests/test_discrete_basic.py,sha256=pAEGn2dqFx7a_qL1h0NHIYAVt0j_CFWg2xxG9ZojhgE,19998
scipy/stats/tests/test_discrete_distns.py,sha256=nSIQK8ucigI5Uh4DRDwwDz66GIWBLeUaRn0iJBuXSFA,23234
scipy/stats/tests/test_distributions.py,sha256=wNghUpeRefI0NqGlBMrgFY1AC2Nj7dL9Y92wKQEoRN8,384251
scipy/stats/tests/test_entropy.py,sha256=92tO5uF3bpqUoU0gpmn89fInuKjVTatXPf5hwh9Kbns,11281
scipy/stats/tests/test_fast_gen_inversion.py,sha256=2FV7tIuHWfjLGO4xMDi4j5poA1zBwEs-tpkwSVDaLrs,15889
scipy/stats/tests/test_fit.py,sha256=GqCiCnEivEGOkloerHmKClzwAzQa-bpvf6-nWVP0Qys,45662
scipy/stats/tests/test_hypotests.py,sha256=e8FUHEowBTmeixb1g9yTpvs5mZofJeRQJmlxVaqHS1o,80302
scipy/stats/tests/test_kdeoth.py,sha256=cCEieP06bjuIrS-V5P7q6T7st0z5zG1AR9KyEywvWew,20470
scipy/stats/tests/test_morestats.py,sha256=p98oupmiQV-dHRe956SJGnq_gvQtvKoMIdgKKFNygkE,128454
scipy/stats/tests/test_mstats_basic.py,sha256=4dvTBP06G8tEbqZwimB9y0HxHGdyor_x21AbUHeqn6o,86407
scipy/stats/tests/test_mstats_extras.py,sha256=CCexzT1lksTG_WvGvHn6-CuWd_ZXoFviNGnBZd_hE7Y,7297
scipy/stats/tests/test_multicomp.py,sha256=xLlLP54cWsLAbSsfodoTkuJa9FJM1qKnlSrDGE-jRZ0,17826
scipy/stats/tests/test_multivariate.py,sha256=naPnWGp6fXMS4ALDnqDd4p2oWmTEqYbczxzTQi5494E,153313
scipy/stats/tests/test_odds_ratio.py,sha256=RIsmgnmUUH3DvynDRZUaS6llCbXm2oWIfPa48IJJ-gI,6705
scipy/stats/tests/test_qmc.py,sha256=MsZ_hgjfxSXpqLlkKrk8x1FJy8ImmZwF2cVrcc1uiKM,54645
scipy/stats/tests/test_rank.py,sha256=uxJXitafsPrfI3yrdVOT1Hiz3abzy5vCRafSnpn_KfU,11721
scipy/stats/tests/test_relative_risk.py,sha256=jzOGNQ2y9_YfFnXiGAiRDrgahy66qQkw6ZkHgygCJMA,3646
scipy/stats/tests/test_resampling.py,sha256=X8uKrXUDZbKETZrPmv5cmHilyfIzyfwj5OPPm5beUyw,71766
scipy/stats/tests/test_sampling.py,sha256=EOtDuGLi87801MG0rkDsJ6n7PfIO8f44n4xjdt0vxY4,54513
scipy/stats/tests/test_sensitivity_analysis.py,sha256=mMifx96zCAx1OOM0Er3ugd_S2I6bih9GF1pir6djNyQ,10134
scipy/stats/tests/test_stats.py,sha256=yNC3SPq7IPFJWZLJxBAZS4z3n_mn8VzVAL8VV1yug8M,360179
scipy/stats/tests/test_survival.py,sha256=ky3R88sMfKUkqTs6wXUTjOjK1BzCWpxS16crycohUps,22265
scipy/stats/tests/test_tukeylambda_stats.py,sha256=6WUBNVoTseVjfrHfWXtU11gTgmRcdnwAPLQOI0y_5U8,3231
scipy/stats/tests/test_variation.py,sha256=Xnsn0fk4lqtk-ji1VhXxTdDAg9fHv02Q6Uv82-Xx6v4,6292
scipy/version.py,sha256=GUwVpI7POtnVQEMN64ypbDj6ZqXtvmTWPo5Q2_lJa2k,264
