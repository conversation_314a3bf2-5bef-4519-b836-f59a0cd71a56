# 配对交易策略回测系统

## 项目概述

本项目实现了基于Backtrader框架的ETF配对交易策略回测系统，使用滚动回归和beta标准误差计算交易区间，实现了完整的策略回测、结果分析和可视化功能。

## 核心特性

### 1. 策略算法
- **滚动回归**：使用RollingOLS计算两个ETF之间的动态beta关系
- **交易区间**：基于beta的标准误差计算上下交易边界
- **信号生成**：当价格比率突破边界时生成交易信号
- **风险控制**：包含盈亏检查机制，避免频繁亏损交易
- **成本考虑**：在交易边界计算中考虑手续费和滑点

### 2. 数据处理
- 支持本地CSV数据文件读取
- 自动解析时间格式和价格数据
- 数据清洗和格式标准化
- 支持每分钟频率的高频数据

### 3. 回测框架
- 基于Backtrader专业回测框架
- 完整的订单管理和资金管理
- 实时手续费和滑点计算
- 多种绩效分析指标

### 4. 可视化分析
- 价格比率和交易信号图表
- 组合收益曲线和回撤分析
- 交易分析和统计图表
- 详细的绩效报告

## 文件结构

```
├── main.py                          # 主程序文件，统一参数管理
├── pairs_strategy.py                # 原始策略逻辑（参考）
├── backtrader_pairs_strategy.py     # Backtrader适配的策略类
├── data_loader.py                   # 数据加载和处理模块
├── visualization.py                 # 结果可视化模块
├── README.md                        # 项目说明文档
├── results/                         # 结果输出目录
│   ├── trades_*.csv                 # 交易记录
│   ├── signals_*.csv                # 信号记录
│   ├── *.png                        # 可视化图表
└── venv/                           # 虚拟环境
```

## 使用方法

### 1. 环境准备

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装依赖包（已完成）
pip install backtrader pandas numpy matplotlib statsmodels
```

### 2. 参数配置

在 `main.py` 中的 `PairsBacktestConfig` 类中修改参数：

```python
class PairsBacktestConfig:
    def __init__(self):
        # 策略参数
        self.window = 10                    # 滚动回归窗口大小
        self.std_dev_mult = 1.2            # beta标准误差的乘数
        self.max_pos_size = 0.95           # 最大持仓比例
        
        # 回测参数
        self.initial_cash = 1000000.0      # 初始资金
        self.commission_rate = 0.0003      # 手续费率
        self.slippage_rate = 0.001         # 滑点率
        
        # 其他配置...
```

### 3. 运行回测

```bash
# 运行完整回测
python main.py

# 生成可视化图表
python visualization.py
```

### 4. 查看结果

回测完成后，结果文件保存在 `results/` 目录：
- `trades_*.csv`: 详细交易记录
- `signals_*.csv`: 交易信号记录
- `*.png`: 可视化图表

## 回测结果

### 基本绩效指标

- **回测期间**: 2022-06-27 至 2025-02-28
- **初始资金**: 1,000,000.00 元
- **最终资金**: 2,514,802.37 元
- **总收益**: 1,514,802.37 元
- **总收益率**: 151.48%
- **夏普比率**: 1.7047
- **最大回撤**: 4.82%

### 交易统计

- **总交易次数**: 811 笔
- **盈利交易**: 472 笔
- **亏损交易**: 339 笔
- **胜率**: 58.20%
- **平均盈利**: 5,131.59 元
- **平均亏损**: -2,676.42 元
- **盈亏比**: 1.92

## 策略逻辑详解

### 1. 滚动回归计算

使用过去N个时间点的数据，计算ETF1对ETF2的回归系数（beta）：

```
ETF1_price = beta × ETF2_price + error
```

### 2. 交易边界计算

基于beta的标准误差计算交易区间：

```
上边界 = beta + std_dev_mult × beta_std_error
下边界 = beta - std_dev_mult × beta_std_error
```

考虑交易成本后的实际边界：

```
实际上边界 = 上边界 × (1 + 手续费 + 滑点)²
实际下边界 = 下边界 / (1 + 手续费 + 滑点)²
```

### 3. 信号生成

```
当前价格比率 = ETF1_price / ETF2_price

if 价格比率 > 实际上边界:
    信号 = -1  # 卖出ETF1，买入ETF2
elif 价格比率 < 实际下边界:
    信号 = 1   # 买入ETF1，卖出ETF2
else:
    信号 = 0   # 无交易
```

## 参数调优建议

### 1. 窗口大小 (window)
- **较小窗口** (5-10): 对价格变化更敏感，交易频率高
- **较大窗口** (15-30): 更稳定，但可能错过短期机会
- **当前设置**: 10（平衡敏感性和稳定性）

### 2. 标准差乘数 (std_dev_mult)
- **较小值** (0.8-1.0): 交易频率高，但可能增加噪音交易
- **较大值** (1.5-2.0): 交易频率低，但信号更可靠
- **当前设置**: 1.2（适中的交易频率）

### 3. 手续费率 (commission_rate)
- 根据实际券商费率设置
- **当前设置**: 0.0003 (0.03%)

## 风险提示

1. **历史回测不代表未来表现**：策略基于历史数据，实际市场环境可能发生变化
2. **交易成本影响**：高频交易会产生较高的交易成本
3. **市场风险**：配对交易假设两个ETF之间存在稳定的统计关系
4. **流动性风险**：需要确保交易的ETF具有足够的流动性
5. **模型风险**：滚动回归模型可能在市场结构性变化时失效

## 扩展功能

### 可能的改进方向

1. **多对配对交易**：支持多个ETF对同时交易
2. **动态参数调整**：根据市场波动率动态调整参数
3. **风险管理**：添加止损、仓位控制等风险管理功能
4. **实时交易**：连接实时数据源和交易接口
5. **机器学习**：使用ML方法优化参数选择

## 技术支持

如有问题或建议，请查看代码注释或联系开发团队。

---

**免责声明**: 本项目仅用于学习和研究目的，不构成投资建议。实际投资请谨慎决策并承担相应风险。
