#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配对交易策略回测主程序
统一管理所有参数设置，执行回测并生成结果
"""

import backtrader as bt
import pandas as pd
import matplotlib.pyplot as plt
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_loader import ETFDataLoader
from backtrader_pairs_strategy_optimized import OptimizedPairsStrategy


class PairsBacktestConfig:
    """配对交易回测配置类 - 统一管理所有参数"""
    
    def __init__(self):
        # ==================== 数据文件配置 ====================
        self.etf1_file = "hq-shkl-518880-6-20250227170356623.csv"  # ETF1数据文件
        self.etf2_file = "hq-szkl-160719-6-20250716151335503.csv"   # ETF2数据文件
        self.etf1_name = "ETF_518880"  # ETF1名称
        self.etf2_name = "ETF_160719"  # ETF2名称
        
        # ==================== 策略参数配置 ====================
        self.window = 120                   # 滚动回归窗口大小
        self.std_dev_mult = 2              # beta标准误差的乘数
        self.max_pos_size = 1              # 最大持仓比例
        self.profit_check_enabled = False   # 是否启用盈亏检查机制
        self.verbose = True                 # 是否输出详细交易日志
        
        # ==================== 回测参数配置 ====================
        self.initial_cash = 1000000.0      # 初始资金（100万）
        self.commission_rate = 0.00005      # 手续费率（0.005%）
        self.slippage_rate = 0.001         # 滑点率（0.1%）
        
        # ==================== 时间范围配置 ====================
        self.start_date = None             # 回测开始日期（None表示使用数据的开始日期）
        self.end_date = None               # 回测结束日期（None表示使用数据的结束日期）
        
        # ==================== 输出配置 ====================
        self.output_dir = "results"        # 结果输出目录
        self.save_plots = True             # 是否保存图表
        self.save_trades = True            # 是否保存交易记录
        self.save_signals = True           # 是否保存信号记录
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def print_config(self):
        """打印当前配置"""
        print("=" * 60)
        print("配对交易策略回测配置")
        print("=" * 60)
        print(f"ETF1文件: {self.etf1_file}")
        print(f"ETF2文件: {self.etf2_file}")
        print(f"ETF1名称: {self.etf1_name}")
        print(f"ETF2名称: {self.etf2_name}")
        print("-" * 40)
        print(f"滚动窗口: {self.window}")
        print(f"标准差乘数: {self.std_dev_mult}")
        print(f"最大持仓比例: {self.max_pos_size}")
        print(f"盈亏检查: {'启用' if self.profit_check_enabled else '禁用'}")
        print("-" * 40)
        print(f"初始资金: {self.initial_cash:,.0f}")
        print(f"手续费率: {self.commission_rate:.4f}")
        print(f"滑点率: {self.slippage_rate:.4f}")
        print("-" * 40)
        print(f"输出目录: {self.output_dir}")
        print("=" * 60)


class PairsBacktester:
    """配对交易回测器"""
    
    def __init__(self, config: PairsBacktestConfig):
        self.config = config
        self.cerebro = None
        self.strategy_instance = None
        self.results = None
        
    def setup_cerebro(self):
        """设置Backtrader引擎"""
        self.cerebro = bt.Cerebro()
        
        # 设置初始资金
        self.cerebro.broker.setcash(self.config.initial_cash)
        
        # 设置手续费
        self.cerebro.broker.setcommission(commission=self.config.commission_rate)
        
        # 添加数据源
        loader = ETFDataLoader()
        
        # 创建数据源
        etf1_feed = loader.create_backtrader_feed(self.config.etf1_file, self.config.etf1_name)
        etf2_feed = loader.create_backtrader_feed(self.config.etf2_file, self.config.etf2_name)
        
        self.cerebro.adddata(etf1_feed)
        self.cerebro.adddata(etf2_feed)
        
        # 添加策略
        self.cerebro.addstrategy(
            OptimizedPairsStrategy,
            window=self.config.window,
            std_dev_mult=self.config.std_dev_mult,
            max_pos_size=self.config.max_pos_size,
            verbose=self.config.verbose,
            profit_check_enabled=self.config.profit_check_enabled
        )
        
        # 添加分析器
        self.cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        self.cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        self.cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        self.cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        if self.config.verbose:
            print(f"Backtrader引擎设置完成")
            print(f"初始资金: {self.cerebro.broker.getcash():,.2f}")
    
    def run_backtest(self):
        """运行回测"""
        if self.cerebro is None:
            self.setup_cerebro()
        
        print("\n开始运行回测...")
        start_time = datetime.now()
        
        # 运行回测
        self.results = self.cerebro.run()
        self.strategy_instance = self.results[0]
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"回测完成，耗时: {duration:.2f}秒")
        print(f"最终资金: {self.cerebro.broker.getvalue():,.2f}")
        print(f"总收益: {self.cerebro.broker.getvalue() - self.config.initial_cash:,.2f}")
        print(f"收益率: {(self.cerebro.broker.getvalue() / self.config.initial_cash - 1) * 100:.2f}%")
    
    def print_performance_metrics(self):
        """打印绩效指标"""
        if not self.results:
            print("请先运行回测")
            return
        
        strategy = self.results[0]
        
        print("\n" + "=" * 60)
        print("绩效指标")
        print("=" * 60)
        
        # 基本指标
        initial_value = self.config.initial_cash
        final_value = self.cerebro.broker.getvalue()
        total_return = (final_value / initial_value - 1) * 100
        
        print(f"初始资金: {initial_value:,.2f}")
        print(f"最终资金: {final_value:,.2f}")
        print(f"总收益: {final_value - initial_value:,.2f}")
        print(f"总收益率: {total_return:.2f}%")
        
        # 分析器结果
        try:
            sharpe = strategy.analyzers.sharpe.get_analysis()
            if 'sharperatio' in sharpe and sharpe['sharperatio'] is not None:
                print(f"夏普比率: {sharpe['sharperatio']:.4f}")
        except:
            print("夏普比率: 无法计算")
        
        try:
            drawdown = strategy.analyzers.drawdown.get_analysis()
            print(f"最大回撤: {drawdown['max']['drawdown']:.2f}%")
        except:
            print("最大回撤: 无法计算")
        
        try:
            trades = strategy.analyzers.trades.get_analysis()
            if 'total' in trades and 'total' in trades['total']:
                total_trades = trades['total']['total']
                won_trades = trades['won']['total'] if 'won' in trades else 0
                win_rate = (won_trades / total_trades * 100) if total_trades > 0 else 0
                print(f"总交易次数: {total_trades}")
                print(f"盈利交易次数: {won_trades}")
                print(f"胜率: {win_rate:.2f}%")
        except:
            print("交易统计: 无法计算")
    
    def save_results(self):
        """保存回测结果"""
        if not self.results:
            print("请先运行回测")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存交易记录
        if self.config.save_trades:
            trades_df = self.strategy_instance.get_trades_dataframe()
            if not trades_df.empty:
                trades_file = os.path.join(self.config.output_dir, f"trades_{timestamp}.csv")
                trades_df.to_csv(trades_file, index=False)
                print(f"交易记录已保存: {trades_file}")
        
        # 保存信号记录
        if self.config.save_signals:
            signals_df = self.strategy_instance.get_signals_dataframe()
            if not signals_df.empty:
                signals_file = os.path.join(self.config.output_dir, f"signals_{timestamp}.csv")
                signals_df.to_csv(signals_file, index=False)
                print(f"信号记录已保存: {signals_file}")
        
        # 保存图表
        if self.config.save_plots:
            try:
                plot_file = os.path.join(self.config.output_dir, f"backtest_plot_{timestamp}.png")
                self.cerebro.plot(style='candlestick', savefig=plot_file)
                print(f"回测图表已保存: {plot_file}")
            except Exception as e:
                print(f"保存图表时出错: {e}")


def main():
    """主函数"""
    # 创建配置
    config = PairsBacktestConfig()
    
    # 可以在这里修改配置参数
    # config.window = 15                    # 修改滚动窗口
    # config.std_dev_mult = 1.5            # 修改标准差乘数
    # config.commission_rate = 0.0005      # 修改手续费率
    # config.initial_cash = 500000         # 修改初始资金
    
    # 打印配置
    config.print_config()
    
    # 检查数据文件是否存在
    if not os.path.exists(config.etf1_file):
        print(f"错误: ETF1数据文件不存在: {config.etf1_file}")
        return
    
    if not os.path.exists(config.etf2_file):
        print(f"错误: ETF2数据文件不存在: {config.etf2_file}")
        return
    
    # 创建回测器并运行
    backtester = PairsBacktester(config)
    
    try:
        # 运行回测
        backtester.run_backtest()
        
        # 打印绩效指标
        backtester.print_performance_metrics()
        
        # 保存结果
        backtester.save_results()
        
        print(f"\n回测完成！结果已保存到 {config.output_dir} 目录")
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
