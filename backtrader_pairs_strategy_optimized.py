import backtrader as bt
import numpy as np
import pandas as pd
from collections import deque, defaultdict


class OptimizedPairsStrategy(bt.Strategy):
    """
    优化版配对交易策略
    - 修复全仓换仓逻辑问题
    - 优化滚动回归计算速度
    - 改进资金管理
    """
    
    params = (
        ('window', 120),             # 滚动回归窗口大小
        ('std_dev_mult', 2.0),       # beta标准误差的乘数
        ('max_pos_size', 1.0),       # 最大持仓比例
        ('verbose', True),           # 是否输出详细日志
        ('profit_check_enabled', False),  # 是否启用盈亏检查
    )
    
    def __init__(self):
        """初始化策略"""
        # 确保有两个数据源
        if len(self.datas) != 2:
            raise ValueError("策略需要两个ETF的数据源")
        
        self.etf1_data = self.datas[0]  # 第一个ETF数据
        self.etf2_data = self.datas[1]  # 第二个ETF数据
        
        # 获取ETF名称
        self.etf1_name = getattr(self.etf1_data, '_name', 'ETF1')
        self.etf2_name = getattr(self.etf2_data, '_name', 'ETF2')
        
        # 使用固定长度的deque存储历史价格数据，提高效率
        max_history = self.params.window + 10  # 保留比窗口稍大的历史数据
        self.price_history = {
            'etf1': deque(maxlen=max_history),
            'etf2': deque(maxlen=max_history)
        }
        self.signals_history = []
        
        # 当前持仓状态
        self.current_position = None  # 'etf1', 'etf2', 或 None
        
        # 盈亏检查相关
        self.last_trade_info = None
        
        # 交易记录
        self.trade_log = []
        
        # 缓存计算结果，避免重复计算
        self.last_signal_data = None
        self.last_calc_time = None
        
        if self.params.verbose:
            print(f"初始化优化配对交易策略: {self.etf1_name} vs {self.etf2_name}")
            print(f"参数: 窗口={self.params.window}, 标准差乘数={self.params.std_dev_mult}")
    
    def next(self):
        """每个时间点的策略逻辑"""
        # 获取当前价格（使用开盘价）
        etf1_price = self.etf1_data.open[0]
        etf2_price = self.etf2_data.open[0]
        
        # 存储历史价格
        self.price_history['etf1'].append(etf1_price)
        self.price_history['etf2'].append(etf2_price)
        
        # 需要足够的历史数据才能进行回归
        if len(self.price_history['etf1']) < self.params.window + 1:
            return
        
        # 计算交易信号
        signal_data = self.calculate_signal_optimized()
        if signal_data is None:
            return
        
        # 存储信号历史（可选，用于分析）
        if len(self.signals_history) % 100 == 0:  # 每100个点存储一次，减少内存使用
            self.signals_history.append({
                'datetime': self.datetime.datetime(),
                'etf1_price': etf1_price,
                'etf2_price': etf2_price,
                **signal_data
            })
        
        # 执行交易逻辑
        self.execute_trading_logic(signal_data, etf1_price, etf2_price)
    
    def calculate_signal_optimized(self):
        """优化的信号计算方法"""
        try:
            # 获取价格数据
            etf1_prices = np.array(self.price_history['etf1'])
            etf2_prices = np.array(self.price_history['etf2'])
            
            # 使用最后window个数据点进行回归
            y = etf1_prices[-self.params.window:]
            x = etf2_prices[-self.params.window:]
            
            # 快速回归计算（避免使用statsmodels）
            n = len(y)
            x_mean = np.mean(x)
            y_mean = np.mean(y)
            
            # 计算beta (斜率)
            numerator = np.sum((x - x_mean) * (y - y_mean))
            denominator = np.sum((x - x_mean) ** 2)

            if denominator == 0:
                return None

            beta = numerator / denominator

            # 计算残差和标准误差 - 修复版本
            y_pred = beta * x + (y_mean - beta * x_mean)  # 加上截距项
            residuals = y - y_pred

            # 计算残差标准差
            if n <= 2:
                return None

            residual_std = np.sqrt(np.sum(residuals ** 2) / (n - 2))  # 自由度为n-2

            # 计算beta的标准误差
            x_var = np.sum((x - x_mean) ** 2)
            if x_var == 0:
                return None

            beta_std = residual_std / np.sqrt(x_var)
            
            if np.isnan(beta) or np.isnan(beta_std) or beta_std == 0:
                return None
            
            # 计算交易边界
            upper_bound = beta + self.params.std_dev_mult * beta_std
            lower_bound = beta - self.params.std_dev_mult * beta_std
            
            # 考虑手续费和滑点的影响
            commission_rate = self.broker.getcommissioninfo(self.etf1_data).p.commission
            slippage_rate = 0.001  # 0.1%的滑点
            
            cost_factor = (1 + commission_rate + slippage_rate) ** 2
            trading_upper = upper_bound * cost_factor
            trading_lower = lower_bound / cost_factor
            
            # 计算当前价格比率
            current_ratio = etf1_prices[-1] / etf2_prices[-1]
            
            # 生成交易信号
            trade_signal = 0
            if current_ratio > trading_upper:
                trade_signal = -1  # 卖出ETF1，买入ETF2
            elif current_ratio < trading_lower:
                trade_signal = 1   # 买入ETF1，卖出ETF2
            
            return {
                'beta': beta,
                'beta_std': beta_std,
                'upper_bound': trading_upper,
                'lower_bound': trading_lower,
                'price_ratio': current_ratio,
                'trade_signal': trade_signal
            }
            
        except Exception as e:
            if self.params.verbose:
                print(f"计算信号时出错: {e}")
            return None
    
    def execute_trading_logic(self, signal_data, etf1_price, etf2_price):
        """执行交易逻辑"""
        trade_signal = signal_data['trade_signal']
        
        if trade_signal == 0:
            return  # 无交易信号
        
        # 确定目标ETF
        target_etf = 'etf1' if trade_signal == 1 else 'etf2'
        
        # 如果需要切换持仓
        if self.current_position != target_etf:
            # 执行交易
            self.execute_position_change_optimized(target_etf, etf1_price, etf2_price, signal_data)
    
    def execute_position_change_optimized(self, target_etf, etf1_price, etf2_price, signal_data):
        """优化的持仓变更执行"""
        if self.params.verbose:
            print(f"\n{self.datetime.datetime()}: 切换持仓到 {target_etf}")
            print(f"价格比率: {signal_data['price_ratio']:.6f}")
            print(f"上界: {signal_data['upper_bound']:.6f}, 下界: {signal_data['lower_bound']:.6f}")
        
        # 获取当前持仓
        etf1_position = self.getposition(self.etf1_data).size
        etf2_position = self.getposition(self.etf2_data).size
        
        # 计算当前组合总价值（关键修复：使用组合价值而不是现金）
        current_portfolio_value = (
            abs(etf1_position) * etf1_price + 
            abs(etf2_position) * etf2_price + 
            self.broker.getcash()
        )
        
        if self.params.verbose:
            print(f"当前组合价值: {current_portfolio_value:,.2f}")
            print(f"当前现金: {self.broker.getcash():,.2f}")
            print(f"ETF1持仓: {etf1_position} 股，价值: {abs(etf1_position) * etf1_price:,.2f}")
            print(f"ETF2持仓: {etf2_position} 股，价值: {abs(etf2_position) * etf2_price:,.2f}")
        
        # 先清空所有持仓
        if etf1_position != 0:
            self.close(self.etf1_data)
            if self.params.verbose:
                print(f"平仓 {self.etf1_name}: {etf1_position} 股")
        
        if etf2_position != 0:
            self.close(self.etf2_data)
            if self.params.verbose:
                print(f"平仓 {self.etf2_name}: {etf2_position} 股")
        
        # 计算可用于新持仓的资金（使用组合总价值）
        available_cash = current_portfolio_value * self.params.max_pos_size
        
        if target_etf == 'etf1':
            target_price = etf1_price
            target_data = self.etf1_data
            target_name = self.etf1_name
        else:
            target_price = etf2_price
            target_data = self.etf2_data
            target_name = self.etf2_name
        
        # 计算可买入的股数
        size = int(available_cash / target_price)
        
        if size > 0:
            self.buy(data=target_data, size=size)
            if self.params.verbose:
                print(f"买入 {target_name}: {size} 股，价格: {target_price:.4f}")
                print(f"买入金额: {size * target_price:,.2f}")
        else:
            if self.params.verbose:
                print(f"警告: 计算的买入股数为0，可用资金: {available_cash:,.2f}, 目标价格: {target_price:.4f}")
        
        # 更新当前持仓状态
        self.current_position = target_etf
        
        # 记录交易信息
        self.record_trade_info(signal_data['price_ratio'], signal_data['trade_signal'], target_etf)
    
    def record_trade_info(self, price_ratio, trade_signal, target_etf):
        """记录交易信息"""
        self.last_trade_info = {
            'timestamp': self.datetime.datetime(),
            'price_ratio': price_ratio,
            'direction': trade_signal,
            'target_etf': target_etf
        }
    
    def notify_trade(self, trade):
        """交易通知"""
        if trade.isclosed:
            self.trade_log.append({
                'datetime': self.datetime.datetime(),
                'data_name': trade.data._name,
                'size': trade.size,
                'price': trade.price,
                'pnl': trade.pnl,
                'pnlcomm': trade.pnlcomm
            })
            
            if self.params.verbose:
                print(f"交易完成: {trade.data._name}, 数量: {trade.size}, "
                      f"价格: {trade.price:.4f}, 盈亏: {trade.pnlcomm:.2f}")
    
    def get_signals_dataframe(self):
        """获取信号历史的DataFrame"""
        if not self.signals_history:
            return pd.DataFrame()
        
        return pd.DataFrame(self.signals_history)
    
    def get_trades_dataframe(self):
        """获取交易历史的DataFrame"""
        if not self.trade_log:
            return pd.DataFrame()
        
        return pd.DataFrame(self.trade_log)
