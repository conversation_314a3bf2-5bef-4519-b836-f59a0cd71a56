import backtrader as bt
import numpy as np
import pandas as pd
from statsmodels.regression.rolling import RollingOLS
from collections import defaultdict


class PairsStrategy(bt.Strategy):
    """
    基于Backtrader的配对交易策略
    使用滚动回归和beta标准误差计算交易区间
    """
    
    params = (
        ('window', 10),              # 滚动回归窗口大小
        ('std_dev_mult', 1.2),       # beta标准误差的乘数
        ('max_pos_size', 0.95),      # 最大持仓比例（留5%现金）
        ('verbose', True),           # 是否输出详细日志
        ('profit_check_enabled', False),  # 是否启用盈亏检查
    )
    
    def __init__(self):
        """初始化策略"""
        # 确保有两个数据源
        if len(self.datas) != 2:
            raise ValueError("策略需要两个ETF的数据源")
        
        self.etf1_data = self.datas[0]  # 第一个ETF数据
        self.etf2_data = self.datas[1]  # 第二个ETF数据
        
        # 获取ETF名称
        self.etf1_name = getattr(self.etf1_data, '_name', 'ETF1')
        self.etf2_name = getattr(self.etf2_data, '_name', 'ETF2')
        
        # 存储历史价格数据用于滚动回归
        self.price_history = defaultdict(list)
        self.signals_history = []
        
        # 当前持仓状态
        self.current_position = None  # 'etf1', 'etf2', 或 None
        
        # 盈亏检查相关
        self.last_trade_info = None
        self.profit_check_log = []
        
        # 交易记录
        self.trade_log = []
        
        if self.params.verbose:
            print(f"初始化配对交易策略: {self.etf1_name} vs {self.etf2_name}")
            print(f"参数: 窗口={self.params.window}, 标准差乘数={self.params.std_dev_mult}")
    
    def next(self):
        """每个时间点的策略逻辑"""
        # 获取当前价格（使用开盘价）
        etf1_price = self.etf1_data.open[0]
        etf2_price = self.etf2_data.open[0]
        
        # 存储历史价格
        self.price_history['etf1'].append(etf1_price)
        self.price_history['etf2'].append(etf2_price)
        
        # 需要足够的历史数据才能进行回归
        if len(self.price_history['etf1']) < self.params.window + 1:
            return
        
        # 计算交易信号
        signal_data = self.calculate_signal()
        if signal_data is None:
            return
        
        # 存储信号历史
        self.signals_history.append({
            'datetime': self.datetime.datetime(),
            'etf1_price': etf1_price,
            'etf2_price': etf2_price,
            **signal_data
        })
        
        # 执行交易逻辑
        self.execute_trading_logic(signal_data, etf1_price, etf2_price)
    
    def calculate_signal(self):
        """计算当前的交易信号"""
        try:
            # 获取最近window+1个数据点用于滚动回归
            recent_length = self.params.window + 1
            etf1_prices = np.array(self.price_history['etf1'][-recent_length:])
            etf2_prices = np.array(self.price_history['etf2'][-recent_length:])
            
            # 创建DataFrame用于回归
            df = pd.DataFrame({
                'etf1': etf1_prices,
                'etf2': etf2_prices
            })
            
            # 执行滚动回归
            y = df['etf1']
            X = df['etf2']
            
            rolling_model = RollingOLS(y, X, window=self.params.window)
            rolling_results = rolling_model.fit()
            
            # 获取最新的beta和标准误差
            latest_beta = rolling_results.params.iloc[-1, 0]
            latest_beta_std = rolling_results.bse.iloc[-1, 0]
            
            if pd.isna(latest_beta) or pd.isna(latest_beta_std):
                return None
            
            # 计算交易边界
            upper_bound = latest_beta + self.params.std_dev_mult * latest_beta_std
            lower_bound = latest_beta - self.params.std_dev_mult * latest_beta_std
            
            # 考虑手续费和滑点的影响
            commission_rate = self.broker.getcommissioninfo(self.etf1_data).p.commission
            slippage_rate = 0.001  # 假设0.1%的滑点
            
            cost_factor = (1 + commission_rate + slippage_rate) ** 2
            trading_upper = upper_bound * cost_factor
            trading_lower = lower_bound / cost_factor
            
            # 计算当前价格比率
            current_ratio = etf1_prices[-1] / etf2_prices[-1]
            
            # 生成交易信号
            trade_signal = 0
            if current_ratio > trading_upper:
                trade_signal = -1  # 卖出ETF1，买入ETF2
            elif current_ratio < trading_lower:
                trade_signal = 1   # 买入ETF1，卖出ETF2
            
            return {
                'beta': latest_beta,
                'beta_std': latest_beta_std,
                'upper_bound': trading_upper,
                'lower_bound': trading_lower,
                'price_ratio': current_ratio,
                'trade_signal': trade_signal
            }
            
        except Exception as e:
            if self.params.verbose:
                print(f"计算信号时出错: {e}")
            return None
    
    def execute_trading_logic(self, signal_data, etf1_price, etf2_price):
        """执行交易逻辑"""
        trade_signal = signal_data['trade_signal']
        
        if trade_signal == 0:
            return  # 无交易信号
        
        # 确定目标ETF
        target_etf = 'etf1' if trade_signal == 1 else 'etf2'
        
        # 如果需要切换持仓
        if self.current_position != target_etf:
            # 执行盈亏检查
            if self.params.profit_check_enabled:
                profit_check = self.check_profit_potential(
                    signal_data['price_ratio'], trade_signal, target_etf
                )
                
                if not profit_check['can_trade']:
                    if self.params.verbose:
                        print(f"{self.datetime.datetime()}: 跳过交易 - {profit_check['reason']}")
                    return
            
            # 执行交易
            self.execute_position_change(target_etf, etf1_price, etf2_price, signal_data)
    
    def execute_position_change(self, target_etf, etf1_price, etf2_price, signal_data):
        """执行持仓变更"""
        if self.params.verbose:
            print(f"\n{self.datetime.datetime()}: 切换持仓到 {target_etf}")
            print(f"价格比率: {signal_data['price_ratio']:.6f}")
            print(f"上界: {signal_data['upper_bound']:.6f}, 下界: {signal_data['lower_bound']:.6f}")
        
        # 先清空所有持仓
        etf1_position = self.getposition(self.etf1_data).size
        etf2_position = self.getposition(self.etf2_data).size
        
        if etf1_position != 0:
            self.close(self.etf1_data)
            if self.params.verbose:
                print(f"平仓 {self.etf1_name}: {etf1_position} 股")
        
        if etf2_position != 0:
            self.close(self.etf2_data)
            if self.params.verbose:
                print(f"平仓 {self.etf2_name}: {etf2_position} 股")
        
        # 计算新持仓大小
        available_cash = self.broker.getcash() * self.params.max_pos_size
        
        if target_etf == 'etf1':
            target_price = etf1_price
            target_data = self.etf1_data
            target_name = self.etf1_name
        else:
            target_price = etf2_price
            target_data = self.etf2_data
            target_name = self.etf2_name
        
        # 计算可买入的股数
        size = int(available_cash / target_price)
        
        if size > 0:
            self.buy(data=target_data, size=size)
            if self.params.verbose:
                print(f"买入 {target_name}: {size} 股，价格: {target_price:.4f}")
        
        # 更新当前持仓状态
        self.current_position = target_etf
        
        # 记录交易信息
        self.record_trade_info(signal_data['price_ratio'], signal_data['trade_signal'], target_etf)
    
    def check_profit_potential(self, current_price_ratio, new_trade_signal, target_etf):
        """检查盈利潜力"""
        check_result = {
            'can_trade': True,
            'reason': '无历史交易记录，允许交易',
            'expected_profit': 0.0
        }
        
        if not self.last_trade_info:
            return check_result
        
        last_trade = self.last_trade_info
        last_price_ratio = last_trade['price_ratio']
        last_direction = last_trade['direction']
        last_target_etf = last_trade['target_etf']
        
        # 如果目标ETF相同，说明不是反向交易
        if target_etf == last_target_etf:
            check_result['reason'] = '相同方向交易，允许执行'
            return check_result
        
        # 计算预期盈亏
        if last_direction == 1:  # 上次买ETF1卖ETF2
            expected_profit = (current_price_ratio - last_price_ratio) / last_price_ratio
            will_profit = current_price_ratio > last_price_ratio
        else:  # 上次卖ETF1买ETF2
            expected_profit = (last_price_ratio - current_price_ratio) / last_price_ratio
            will_profit = current_price_ratio < last_price_ratio
        
        check_result['expected_profit'] = expected_profit
        
        # 考虑交易成本
        commission_rate = self.broker.getcommissioninfo(self.etf1_data).p.commission
        total_cost = 2 * (commission_rate + 0.001)  # 双边成本
        
        if will_profit and expected_profit > total_cost:
            check_result['can_trade'] = True
            check_result['reason'] = f'预期盈利 {expected_profit:.4f} > 交易成本 {total_cost:.4f}'
        else:
            check_result['can_trade'] = False
            check_result['reason'] = f'预期盈利不足或会亏损，预期收益率 {expected_profit:.4f}'
        
        return check_result
    
    def record_trade_info(self, price_ratio, trade_signal, target_etf):
        """记录交易信息"""
        self.last_trade_info = {
            'timestamp': self.datetime.datetime(),
            'price_ratio': price_ratio,
            'direction': trade_signal,
            'target_etf': target_etf
        }
    
    def notify_trade(self, trade):
        """交易通知"""
        if trade.isclosed:
            self.trade_log.append({
                'datetime': self.datetime.datetime(),
                'data_name': trade.data._name,
                'size': trade.size,
                'price': trade.price,
                'pnl': trade.pnl,
                'pnlcomm': trade.pnlcomm
            })
            
            if self.params.verbose:
                print(f"交易完成: {trade.data._name}, 数量: {trade.size}, "
                      f"价格: {trade.price:.4f}, 盈亏: {trade.pnlcomm:.2f}")
    
    def get_signals_dataframe(self):
        """获取信号历史的DataFrame"""
        if not self.signals_history:
            return pd.DataFrame()
        
        return pd.DataFrame(self.signals_history)
    
    def get_trades_dataframe(self):
        """获取交易历史的DataFrame"""
        if not self.trade_log:
            return pd.DataFrame()
        
        return pd.DataFrame(self.trade_log)
